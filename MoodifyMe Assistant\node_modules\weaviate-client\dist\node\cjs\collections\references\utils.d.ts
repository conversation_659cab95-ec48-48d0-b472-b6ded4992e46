import { ReferenceInput } from '../types/index.js';
import { ReferenceManager } from './classes.js';
import { Beacon } from './types.js';
export declare function uuidToBeacon(uuid: string, targetCollection?: string): Beacon;
export declare const referenceFromObjects: <TProperties>(
  objects: any[],
  targetCollection: string,
  uuids: string[]
) => ReferenceManager<TProperties>;
export declare const referenceToBeacons: <T>(ref: ReferenceInput<T>) => Beacon[];
