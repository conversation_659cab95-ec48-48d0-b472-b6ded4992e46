'use strict';
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.176.0
//   protoc               v3.19.1
// source: v1/weaviate.proto
Object.defineProperty(exports, '__esModule', { value: true });
exports.WeaviateDefinition = exports.protobufPackage = void 0;
const aggregate_js_1 = require('./aggregate.js');
const batch_js_1 = require('./batch.js');
const batch_delete_js_1 = require('./batch_delete.js');
const search_get_js_1 = require('./search_get.js');
const tenants_js_1 = require('./tenants.js');
exports.protobufPackage = 'weaviate.v1';
exports.WeaviateDefinition = {
  name: 'Weaviate',
  fullName: 'weaviate.v1.Weaviate',
  methods: {
    search: {
      name: 'Search',
      requestType: search_get_js_1.SearchRequest,
      requestStream: false,
      responseType: search_get_js_1.SearchReply,
      responseStream: false,
      options: {},
    },
    batchObjects: {
      name: 'BatchObjects',
      requestType: batch_js_1.BatchObjectsRequest,
      requestStream: false,
      responseType: batch_js_1.BatchObjectsReply,
      responseStream: false,
      options: {},
    },
    batchDelete: {
      name: 'BatchDelete',
      requestType: batch_delete_js_1.BatchDeleteRequest,
      requestStream: false,
      responseType: batch_delete_js_1.BatchDeleteReply,
      responseStream: false,
      options: {},
    },
    tenantsGet: {
      name: 'TenantsGet',
      requestType: tenants_js_1.TenantsGetRequest,
      requestStream: false,
      responseType: tenants_js_1.TenantsGetReply,
      responseStream: false,
      options: {},
    },
    aggregate: {
      name: 'Aggregate',
      requestType: aggregate_js_1.AggregateRequest,
      requestStream: false,
      responseType: aggregate_js_1.AggregateReply,
      responseStream: false,
      options: {},
    },
  },
};
