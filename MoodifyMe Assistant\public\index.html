<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MoodifyMe Therapist - AI-Powered Therapeutic Support</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧠</text></svg>">
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-brain"></i>
                        <div class="pulse-ring"></div>
                    </div>
                    <div class="logo-text">
                        <h1>MoodifyMe Therapist</h1>
                        <p class="tagline">Your AI Therapeutic Companion</p>
                    </div>
                    <div id="connectionBadge" class="connection-badge animate__animated animate__fadeInDown" style="display: none;">
                        <i class="fas fa-link"></i> Connected from MoodifyMe
                    </div>
                </div>
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">Connecting...</span>
                    <a href="#" id="backToRecommendations" class="back-link" style="display: none;">
                        <i class="fas fa-arrow-left"></i> Back to Recommendations
                    </a>
                </div>
            </div>
        </header>

        <!-- Main Chat Interface -->
        <main class="chat-container">
            <!-- Welcome Message -->
            <div class="welcome-message" id="welcomeMessage">
                <div class="welcome-content animate__animated animate__fadeInUp">
                    <div class="welcome-icon">
                        <i class="fas fa-heart"></i>
                        <div class="heart-pulse"></div>
                    </div>
                    <h2>Welcome to Your Safe Space</h2>
                    <p class="welcome-subtitle">I'm Chat-Tevez, your AI therapeutic companion</p>
                    <p class="welcome-description">I provide professional therapeutic support using evidence-based techniques like CBT, DBT, and mindfulness. I'm here to help you process emotions, develop coping strategies, and support your mental health journey.</p>

                    <div class="therapeutic-features">
                        <div class="feature-item">
                            <i class="fas fa-shield-heart"></i>
                            <span>Crisis Support</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-brain"></i>
                            <span>CBT & DBT Techniques</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-leaf"></i>
                            <span>Mindfulness Practice</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-hands-helping"></i>
                            <span>Emotional Support</span>
                        </div>
                    </div>

                    <div class="quick-actions">
                        <button class="quick-btn therapeutic" onclick="sendQuickMessage('I am feeling sad and need support')">
                            <i class="fas fa-heart-broken"></i> I need emotional support
                        </button>
                        <button class="quick-btn therapeutic" onclick="sendQuickMessage('I am feeling anxious and overwhelmed')">
                            <i class="fas fa-cloud-rain"></i> I'm feeling anxious
                        </button>
                        <button class="quick-btn therapeutic" onclick="sendQuickMessage('Help me with coping strategies')">
                            <i class="fas fa-tools"></i> Teach me coping skills
                        </button>
                        <button class="quick-btn therapeutic" onclick="sendQuickMessage('I want to practice mindfulness')">
                            <i class="fas fa-leaf"></i> Mindfulness practice
                        </button>
                    </div>

                    <div class="disclaimer">
                        <i class="fas fa-info-circle"></i>
                        <p>I provide therapeutic support but am not a replacement for professional therapy. In crisis situations, please contact emergency services or crisis hotlines.</p>
                    </div>
                </div>
            </div>

            <!-- Chat Messages -->
            <div class="chat-messages" id="chatMessages">
                <!-- Messages will be dynamically added here -->
            </div>

            <!-- Typing Indicator -->
            <div class="typing-indicator" id="typingIndicator" style="display: none;">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <span>Chat-Tevez is providing therapeutic support...</span>
            </div>
        </main>

        <!-- Chat Input -->
        <footer class="chat-input-container">
            <div class="chat-input-wrapper">
                <div class="input-group">
                    <textarea
                        id="messageInput"
                        placeholder="Share your thoughts, feelings, or what's on your mind today..."
                        rows="1"
                        maxlength="1000"
                    ></textarea>
                    <div class="input-actions">
                        <button id="voiceBtn" class="action-btn voice-btn" title="Voice input">
                            <i class="fas fa-microphone"></i>
                        </button>
                        <button id="sendBtn" class="send-btn" title="Send message">
                            <i class="fas fa-arrow-up"></i>
                        </button>
                    </div>
                </div>
                <div class="input-footer">
                    <div class="char-counter">
                        <span id="charCount">0</span>/1000
                    </div>
                    <div class="mood-display" id="moodDisplay" style="display: none;">
                        Detected mood: <span id="currentMood"></span>
                    </div>
                </div>
            </div>
        </footer>

        <!-- Conversation History Panel -->
        <div class="history-panel" id="historyPanel">
            <div class="history-header">
                <h3>Conversation History</h3>
                <button id="clearHistoryBtn" class="clear-btn">
                    <i class="fas fa-trash"></i> Clear
                </button>
            </div>
            <div class="history-content" id="historyContent">
                <!-- History items will be added here -->
            </div>
        </div>

        <!-- Settings Panel -->
        <div class="settings-panel" id="settingsPanel">
            <div class="settings-header">
                <h3>Settings</h3>
                <button id="closeSettingsBtn" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="settings-content">
                <div class="setting-group">
                    <label for="themeSelect">Theme</label>
                    <select id="themeSelect">
                        <option value="light">Light</option>
                        <option value="dark">Dark</option>
                        <option value="auto">Auto</option>
                    </select>
                </div>
                <div class="setting-group">
                    <label for="autoJoke">Auto-suggest jokes for sad moods</label>
                    <input type="checkbox" id="autoJoke">
                </div>
                <div class="setting-group">
                    <label for="soundEnabled">Sound notifications</label>
                    <input type="checkbox" id="soundEnabled">
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button id="historyToggleBtn" class="action-btn" title="Toggle history">
                <i class="fas fa-history"></i>
            </button>
            <button id="settingsBtn" class="action-btn" title="Settings">
                <i class="fas fa-cog"></i>
            </button>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-brain fa-spin"></i>
            <p>Initializing MoodifyMe Assistant...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="app.js"></script>
</body>
</html>
