"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.wcswidth = exports.wcwidth = void 0;
const wcswidth_1 = __importDefault(require("./src/wcswidth"));
exports.wcswidth = wcswidth_1.default;
const wcwidth_1 = __importDefault(require("./src/wcwidth"));
exports.wcwidth = wcwidth_1.default;
