// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.176.0
//   protoc               v3.19.1
// source: v1/properties.proto
/* eslint-disable */
import Long from 'long';
import _m0 from 'protobufjs/minimal.js';
import { nullValueFromJSON, nullValueToJSON } from '../google/protobuf/struct.js';
export const protobufPackage = 'weaviate.v1';
function createBaseProperties() {
  return { fields: {} };
}
export const Properties = {
  encode(message, writer = _m0.Writer.create()) {
    Object.entries(message.fields).forEach(([key, value]) => {
      Properties_FieldsEntry.encode({ key: key, value }, writer.uint32(10).fork()).ldelim();
    });
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProperties();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          const entry1 = Properties_FieldsEntry.decode(reader, reader.uint32());
          if (entry1.value !== undefined) {
            message.fields[entry1.key] = entry1.value;
          }
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      fields: isObject(object.fields)
        ? Object.entries(object.fields).reduce((acc, [key, value]) => {
            acc[key] = Value.fromJSON(value);
            return acc;
          }, {})
        : {},
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.fields) {
      const entries = Object.entries(message.fields);
      if (entries.length > 0) {
        obj.fields = {};
        entries.forEach(([k, v]) => {
          obj.fields[k] = Value.toJSON(v);
        });
      }
    }
    return obj;
  },
  create(base) {
    return Properties.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseProperties();
    message.fields = Object.entries((_a = object.fields) !== null && _a !== void 0 ? _a : {}).reduce(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = Value.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  },
};
function createBaseProperties_FieldsEntry() {
  return { key: '', value: undefined };
}
export const Properties_FieldsEntry = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.key !== '') {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== undefined) {
      Value.encode(message.value, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseProperties_FieldsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.value = Value.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? Value.fromJSON(object.value) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.key !== '') {
      obj.key = message.key;
    }
    if (message.value !== undefined) {
      obj.value = Value.toJSON(message.value);
    }
    return obj;
  },
  create(base) {
    return Properties_FieldsEntry.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseProperties_FieldsEntry();
    message.key = (_a = object.key) !== null && _a !== void 0 ? _a : '';
    message.value =
      object.value !== undefined && object.value !== null ? Value.fromPartial(object.value) : undefined;
    return message;
  },
};
function createBaseValue() {
  return {
    numberValue: undefined,
    stringValue: undefined,
    boolValue: undefined,
    objectValue: undefined,
    listValue: undefined,
    dateValue: undefined,
    uuidValue: undefined,
    intValue: undefined,
    geoValue: undefined,
    blobValue: undefined,
    phoneValue: undefined,
    nullValue: undefined,
    textValue: undefined,
  };
}
export const Value = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.numberValue !== undefined) {
      writer.uint32(9).double(message.numberValue);
    }
    if (message.stringValue !== undefined) {
      writer.uint32(18).string(message.stringValue);
    }
    if (message.boolValue !== undefined) {
      writer.uint32(24).bool(message.boolValue);
    }
    if (message.objectValue !== undefined) {
      Properties.encode(message.objectValue, writer.uint32(34).fork()).ldelim();
    }
    if (message.listValue !== undefined) {
      ListValue.encode(message.listValue, writer.uint32(42).fork()).ldelim();
    }
    if (message.dateValue !== undefined) {
      writer.uint32(50).string(message.dateValue);
    }
    if (message.uuidValue !== undefined) {
      writer.uint32(58).string(message.uuidValue);
    }
    if (message.intValue !== undefined) {
      writer.uint32(64).int64(message.intValue);
    }
    if (message.geoValue !== undefined) {
      GeoCoordinate.encode(message.geoValue, writer.uint32(74).fork()).ldelim();
    }
    if (message.blobValue !== undefined) {
      writer.uint32(82).string(message.blobValue);
    }
    if (message.phoneValue !== undefined) {
      PhoneNumber.encode(message.phoneValue, writer.uint32(90).fork()).ldelim();
    }
    if (message.nullValue !== undefined) {
      writer.uint32(96).int32(message.nullValue);
    }
    if (message.textValue !== undefined) {
      writer.uint32(106).string(message.textValue);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }
          message.numberValue = reader.double();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.stringValue = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.boolValue = reader.bool();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.objectValue = Properties.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.listValue = ListValue.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.dateValue = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.uuidValue = reader.string();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }
          message.intValue = longToNumber(reader.int64());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.geoValue = GeoCoordinate.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.blobValue = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }
          message.phoneValue = PhoneNumber.decode(reader, reader.uint32());
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }
          message.nullValue = reader.int32();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }
          message.textValue = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      numberValue: isSet(object.numberValue) ? globalThis.Number(object.numberValue) : undefined,
      stringValue: isSet(object.stringValue) ? globalThis.String(object.stringValue) : undefined,
      boolValue: isSet(object.boolValue) ? globalThis.Boolean(object.boolValue) : undefined,
      objectValue: isSet(object.objectValue) ? Properties.fromJSON(object.objectValue) : undefined,
      listValue: isSet(object.listValue) ? ListValue.fromJSON(object.listValue) : undefined,
      dateValue: isSet(object.dateValue) ? globalThis.String(object.dateValue) : undefined,
      uuidValue: isSet(object.uuidValue) ? globalThis.String(object.uuidValue) : undefined,
      intValue: isSet(object.intValue) ? globalThis.Number(object.intValue) : undefined,
      geoValue: isSet(object.geoValue) ? GeoCoordinate.fromJSON(object.geoValue) : undefined,
      blobValue: isSet(object.blobValue) ? globalThis.String(object.blobValue) : undefined,
      phoneValue: isSet(object.phoneValue) ? PhoneNumber.fromJSON(object.phoneValue) : undefined,
      nullValue: isSet(object.nullValue) ? nullValueFromJSON(object.nullValue) : undefined,
      textValue: isSet(object.textValue) ? globalThis.String(object.textValue) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.numberValue !== undefined) {
      obj.numberValue = message.numberValue;
    }
    if (message.stringValue !== undefined) {
      obj.stringValue = message.stringValue;
    }
    if (message.boolValue !== undefined) {
      obj.boolValue = message.boolValue;
    }
    if (message.objectValue !== undefined) {
      obj.objectValue = Properties.toJSON(message.objectValue);
    }
    if (message.listValue !== undefined) {
      obj.listValue = ListValue.toJSON(message.listValue);
    }
    if (message.dateValue !== undefined) {
      obj.dateValue = message.dateValue;
    }
    if (message.uuidValue !== undefined) {
      obj.uuidValue = message.uuidValue;
    }
    if (message.intValue !== undefined) {
      obj.intValue = Math.round(message.intValue);
    }
    if (message.geoValue !== undefined) {
      obj.geoValue = GeoCoordinate.toJSON(message.geoValue);
    }
    if (message.blobValue !== undefined) {
      obj.blobValue = message.blobValue;
    }
    if (message.phoneValue !== undefined) {
      obj.phoneValue = PhoneNumber.toJSON(message.phoneValue);
    }
    if (message.nullValue !== undefined) {
      obj.nullValue = nullValueToJSON(message.nullValue);
    }
    if (message.textValue !== undefined) {
      obj.textValue = message.textValue;
    }
    return obj;
  },
  create(base) {
    return Value.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j;
    const message = createBaseValue();
    message.numberValue = (_a = object.numberValue) !== null && _a !== void 0 ? _a : undefined;
    message.stringValue = (_b = object.stringValue) !== null && _b !== void 0 ? _b : undefined;
    message.boolValue = (_c = object.boolValue) !== null && _c !== void 0 ? _c : undefined;
    message.objectValue =
      object.objectValue !== undefined && object.objectValue !== null
        ? Properties.fromPartial(object.objectValue)
        : undefined;
    message.listValue =
      object.listValue !== undefined && object.listValue !== null
        ? ListValue.fromPartial(object.listValue)
        : undefined;
    message.dateValue = (_d = object.dateValue) !== null && _d !== void 0 ? _d : undefined;
    message.uuidValue = (_e = object.uuidValue) !== null && _e !== void 0 ? _e : undefined;
    message.intValue = (_f = object.intValue) !== null && _f !== void 0 ? _f : undefined;
    message.geoValue =
      object.geoValue !== undefined && object.geoValue !== null
        ? GeoCoordinate.fromPartial(object.geoValue)
        : undefined;
    message.blobValue = (_g = object.blobValue) !== null && _g !== void 0 ? _g : undefined;
    message.phoneValue =
      object.phoneValue !== undefined && object.phoneValue !== null
        ? PhoneNumber.fromPartial(object.phoneValue)
        : undefined;
    message.nullValue = (_h = object.nullValue) !== null && _h !== void 0 ? _h : undefined;
    message.textValue = (_j = object.textValue) !== null && _j !== void 0 ? _j : undefined;
    return message;
  },
};
function createBaseListValue() {
  return {
    values: [],
    numberValues: undefined,
    boolValues: undefined,
    objectValues: undefined,
    dateValues: undefined,
    uuidValues: undefined,
    intValues: undefined,
    textValues: undefined,
  };
}
export const ListValue = {
  encode(message, writer = _m0.Writer.create()) {
    for (const v of message.values) {
      Value.encode(v, writer.uint32(10).fork()).ldelim();
    }
    if (message.numberValues !== undefined) {
      NumberValues.encode(message.numberValues, writer.uint32(18).fork()).ldelim();
    }
    if (message.boolValues !== undefined) {
      BoolValues.encode(message.boolValues, writer.uint32(26).fork()).ldelim();
    }
    if (message.objectValues !== undefined) {
      ObjectValues.encode(message.objectValues, writer.uint32(34).fork()).ldelim();
    }
    if (message.dateValues !== undefined) {
      DateValues.encode(message.dateValues, writer.uint32(42).fork()).ldelim();
    }
    if (message.uuidValues !== undefined) {
      UuidValues.encode(message.uuidValues, writer.uint32(50).fork()).ldelim();
    }
    if (message.intValues !== undefined) {
      IntValues.encode(message.intValues, writer.uint32(58).fork()).ldelim();
    }
    if (message.textValues !== undefined) {
      TextValues.encode(message.textValues, writer.uint32(66).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseListValue();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(Value.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.numberValues = NumberValues.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.boolValues = BoolValues.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.objectValues = ObjectValues.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.dateValues = DateValues.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.uuidValues = UuidValues.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.intValues = IntValues.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }
          message.textValues = TextValues.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => Value.fromJSON(e))
        : [],
      numberValues: isSet(object.numberValues) ? NumberValues.fromJSON(object.numberValues) : undefined,
      boolValues: isSet(object.boolValues) ? BoolValues.fromJSON(object.boolValues) : undefined,
      objectValues: isSet(object.objectValues) ? ObjectValues.fromJSON(object.objectValues) : undefined,
      dateValues: isSet(object.dateValues) ? DateValues.fromJSON(object.dateValues) : undefined,
      uuidValues: isSet(object.uuidValues) ? UuidValues.fromJSON(object.uuidValues) : undefined,
      intValues: isSet(object.intValues) ? IntValues.fromJSON(object.intValues) : undefined,
      textValues: isSet(object.textValues) ? TextValues.fromJSON(object.textValues) : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values.map((e) => Value.toJSON(e));
    }
    if (message.numberValues !== undefined) {
      obj.numberValues = NumberValues.toJSON(message.numberValues);
    }
    if (message.boolValues !== undefined) {
      obj.boolValues = BoolValues.toJSON(message.boolValues);
    }
    if (message.objectValues !== undefined) {
      obj.objectValues = ObjectValues.toJSON(message.objectValues);
    }
    if (message.dateValues !== undefined) {
      obj.dateValues = DateValues.toJSON(message.dateValues);
    }
    if (message.uuidValues !== undefined) {
      obj.uuidValues = UuidValues.toJSON(message.uuidValues);
    }
    if (message.intValues !== undefined) {
      obj.intValues = IntValues.toJSON(message.intValues);
    }
    if (message.textValues !== undefined) {
      obj.textValues = TextValues.toJSON(message.textValues);
    }
    return obj;
  },
  create(base) {
    return ListValue.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseListValue();
    message.values =
      ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => Value.fromPartial(e))) || [];
    message.numberValues =
      object.numberValues !== undefined && object.numberValues !== null
        ? NumberValues.fromPartial(object.numberValues)
        : undefined;
    message.boolValues =
      object.boolValues !== undefined && object.boolValues !== null
        ? BoolValues.fromPartial(object.boolValues)
        : undefined;
    message.objectValues =
      object.objectValues !== undefined && object.objectValues !== null
        ? ObjectValues.fromPartial(object.objectValues)
        : undefined;
    message.dateValues =
      object.dateValues !== undefined && object.dateValues !== null
        ? DateValues.fromPartial(object.dateValues)
        : undefined;
    message.uuidValues =
      object.uuidValues !== undefined && object.uuidValues !== null
        ? UuidValues.fromPartial(object.uuidValues)
        : undefined;
    message.intValues =
      object.intValues !== undefined && object.intValues !== null
        ? IntValues.fromPartial(object.intValues)
        : undefined;
    message.textValues =
      object.textValues !== undefined && object.textValues !== null
        ? TextValues.fromPartial(object.textValues)
        : undefined;
    return message;
  },
};
function createBaseNumberValues() {
  return { values: new Uint8Array(0) };
}
export const NumberValues = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.values.length !== 0) {
      writer.uint32(10).bytes(message.values);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNumberValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values = reader.bytes();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return { values: isSet(object.values) ? bytesFromBase64(object.values) : new Uint8Array(0) };
  },
  toJSON(message) {
    const obj = {};
    if (message.values.length !== 0) {
      obj.values = base64FromBytes(message.values);
    }
    return obj;
  },
  create(base) {
    return NumberValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseNumberValues();
    message.values = (_a = object.values) !== null && _a !== void 0 ? _a : new Uint8Array(0);
    return message;
  },
};
function createBaseTextValues() {
  return { values: [] };
}
export const TextValues = {
  encode(message, writer = _m0.Writer.create()) {
    for (const v of message.values) {
      writer.uint32(10).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTextValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    return obj;
  },
  create(base) {
    return TextValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseTextValues();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
};
function createBaseBoolValues() {
  return { values: [] };
}
export const BoolValues = {
  encode(message, writer = _m0.Writer.create()) {
    writer.uint32(10).fork();
    for (const v of message.values) {
      writer.bool(v);
    }
    writer.ldelim();
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBoolValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 8) {
            message.values.push(reader.bool());
            continue;
          }
          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.values.push(reader.bool());
            }
            continue;
          }
          break;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.Boolean(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    return obj;
  },
  create(base) {
    return BoolValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseBoolValues();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
};
function createBaseObjectValues() {
  return { values: [] };
}
export const ObjectValues = {
  encode(message, writer = _m0.Writer.create()) {
    for (const v of message.values) {
      Properties.encode(v, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseObjectValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(Properties.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => Properties.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values.map((e) => Properties.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return ObjectValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseObjectValues();
    message.values =
      ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => Properties.fromPartial(e))) ||
      [];
    return message;
  },
};
function createBaseDateValues() {
  return { values: [] };
}
export const DateValues = {
  encode(message, writer = _m0.Writer.create()) {
    for (const v of message.values) {
      writer.uint32(10).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseDateValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    return obj;
  },
  create(base) {
    return DateValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseDateValues();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
};
function createBaseUuidValues() {
  return { values: [] };
}
export const UuidValues = {
  encode(message, writer = _m0.Writer.create()) {
    for (const v of message.values) {
      writer.uint32(10).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUuidValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      values: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.values)
        ? object.values.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.values) === null || _a === void 0 ? void 0 : _a.length) {
      obj.values = message.values;
    }
    return obj;
  },
  create(base) {
    return UuidValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseUuidValues();
    message.values = ((_a = object.values) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    return message;
  },
};
function createBaseIntValues() {
  return { values: new Uint8Array(0) };
}
export const IntValues = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.values.length !== 0) {
      writer.uint32(10).bytes(message.values);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseIntValues();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.values = reader.bytes();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return { values: isSet(object.values) ? bytesFromBase64(object.values) : new Uint8Array(0) };
  },
  toJSON(message) {
    const obj = {};
    if (message.values.length !== 0) {
      obj.values = base64FromBytes(message.values);
    }
    return obj;
  },
  create(base) {
    return IntValues.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseIntValues();
    message.values = (_a = object.values) !== null && _a !== void 0 ? _a : new Uint8Array(0);
    return message;
  },
};
function createBaseGeoCoordinate() {
  return { longitude: 0, latitude: 0 };
}
export const GeoCoordinate = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.longitude !== 0) {
      writer.uint32(13).float(message.longitude);
    }
    if (message.latitude !== 0) {
      writer.uint32(21).float(message.latitude);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGeoCoordinate();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 13) {
            break;
          }
          message.longitude = reader.float();
          continue;
        case 2:
          if (tag !== 21) {
            break;
          }
          message.latitude = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      longitude: isSet(object.longitude) ? globalThis.Number(object.longitude) : 0,
      latitude: isSet(object.latitude) ? globalThis.Number(object.latitude) : 0,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.longitude !== 0) {
      obj.longitude = message.longitude;
    }
    if (message.latitude !== 0) {
      obj.latitude = message.latitude;
    }
    return obj;
  },
  create(base) {
    return GeoCoordinate.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseGeoCoordinate();
    message.longitude = (_a = object.longitude) !== null && _a !== void 0 ? _a : 0;
    message.latitude = (_b = object.latitude) !== null && _b !== void 0 ? _b : 0;
    return message;
  },
};
function createBasePhoneNumber() {
  return {
    countryCode: 0,
    defaultCountry: '',
    input: '',
    internationalFormatted: '',
    national: 0,
    nationalFormatted: '',
    valid: false,
  };
}
export const PhoneNumber = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.countryCode !== 0) {
      writer.uint32(8).uint64(message.countryCode);
    }
    if (message.defaultCountry !== '') {
      writer.uint32(18).string(message.defaultCountry);
    }
    if (message.input !== '') {
      writer.uint32(26).string(message.input);
    }
    if (message.internationalFormatted !== '') {
      writer.uint32(34).string(message.internationalFormatted);
    }
    if (message.national !== 0) {
      writer.uint32(40).uint64(message.national);
    }
    if (message.nationalFormatted !== '') {
      writer.uint32(50).string(message.nationalFormatted);
    }
    if (message.valid !== false) {
      writer.uint32(56).bool(message.valid);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePhoneNumber();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.countryCode = longToNumber(reader.uint64());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.defaultCountry = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.input = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.internationalFormatted = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.national = longToNumber(reader.uint64());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.nationalFormatted = reader.string();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }
          message.valid = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      countryCode: isSet(object.countryCode) ? globalThis.Number(object.countryCode) : 0,
      defaultCountry: isSet(object.defaultCountry) ? globalThis.String(object.defaultCountry) : '',
      input: isSet(object.input) ? globalThis.String(object.input) : '',
      internationalFormatted: isSet(object.internationalFormatted)
        ? globalThis.String(object.internationalFormatted)
        : '',
      national: isSet(object.national) ? globalThis.Number(object.national) : 0,
      nationalFormatted: isSet(object.nationalFormatted) ? globalThis.String(object.nationalFormatted) : '',
      valid: isSet(object.valid) ? globalThis.Boolean(object.valid) : false,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.countryCode !== 0) {
      obj.countryCode = Math.round(message.countryCode);
    }
    if (message.defaultCountry !== '') {
      obj.defaultCountry = message.defaultCountry;
    }
    if (message.input !== '') {
      obj.input = message.input;
    }
    if (message.internationalFormatted !== '') {
      obj.internationalFormatted = message.internationalFormatted;
    }
    if (message.national !== 0) {
      obj.national = Math.round(message.national);
    }
    if (message.nationalFormatted !== '') {
      obj.nationalFormatted = message.nationalFormatted;
    }
    if (message.valid !== false) {
      obj.valid = message.valid;
    }
    return obj;
  },
  create(base) {
    return PhoneNumber.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g;
    const message = createBasePhoneNumber();
    message.countryCode = (_a = object.countryCode) !== null && _a !== void 0 ? _a : 0;
    message.defaultCountry = (_b = object.defaultCountry) !== null && _b !== void 0 ? _b : '';
    message.input = (_c = object.input) !== null && _c !== void 0 ? _c : '';
    message.internationalFormatted = (_d = object.internationalFormatted) !== null && _d !== void 0 ? _d : '';
    message.national = (_e = object.national) !== null && _e !== void 0 ? _e : 0;
    message.nationalFormatted = (_f = object.nationalFormatted) !== null && _f !== void 0 ? _f : '';
    message.valid = (_g = object.valid) !== null && _g !== void 0 ? _g : false;
    return message;
  },
};
function bytesFromBase64(b64) {
  if (globalThis.Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}
function base64FromBytes(arr) {
  if (globalThis.Buffer) {
    return globalThis.Buffer.from(arr).toString('base64');
  } else {
    const bin = [];
    arr.forEach((byte) => {
      bin.push(globalThis.String.fromCharCode(byte));
    });
    return globalThis.btoa(bin.join(''));
  }
}
function longToNumber(long) {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error('Value is larger than Number.MAX_SAFE_INTEGER');
  }
  return long.toNumber();
}
if (_m0.util.Long !== Long) {
  _m0.util.Long = Long;
  _m0.configure();
}
function isObject(value) {
  return typeof value === 'object' && value !== null;
}
function isSet(value) {
  return value !== null && value !== undefined;
}
