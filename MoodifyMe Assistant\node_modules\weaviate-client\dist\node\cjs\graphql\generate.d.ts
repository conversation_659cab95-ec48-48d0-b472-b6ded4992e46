export interface GenerateArgs {
  groupedTask?: string;
  groupedProperties?: string[];
  singlePrompt?: string;
}
export interface GenerateParts {
  singleResult?: string;
  groupedResult?: string;
  results: string[];
}
export declare class GraphQLGenerate {
  private groupedTask?;
  private groupedProperties?;
  private singlePrompt?;
  constructor(args: GenerateArgs);
  toString(): string;
  private validate;
}
