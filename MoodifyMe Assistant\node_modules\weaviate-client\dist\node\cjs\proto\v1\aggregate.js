'use strict';
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.176.0
//   protoc               v3.19.1
// source: v1/aggregate.proto
var __importDefault =
  (this && this.__importDefault) ||
  function (mod) {
    return mod && mod.__esModule ? mod : { default: mod };
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.AggregateReply_Grouped =
  exports.AggregateReply_Group_GroupedBy =
  exports.AggregateReply_Group =
  exports.AggregateReply_Single =
  exports.AggregateReply_Aggregations_Aggregation_Reference =
  exports.AggregateReply_Aggregations_Aggregation_DateMessage =
  exports.AggregateReply_Aggregations_Aggregation_Boolean =
  exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences_TopOccurrence =
  exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences =
  exports.AggregateReply_Aggregations_Aggregation_Text =
  exports.AggregateReply_Aggregations_Aggregation_Number =
  exports.AggregateReply_Aggregations_Aggregation_Integer =
  exports.AggregateReply_Aggregations_Aggregation =
  exports.AggregateReply_Aggregations =
  exports.AggregateReply =
  exports.AggregateRequest_GroupBy =
  exports.AggregateRequest_Aggregation_Reference =
  exports.AggregateRequest_Aggregation_DateMessage =
  exports.AggregateRequest_Aggregation_Boolean =
  exports.AggregateRequest_Aggregation_Text =
  exports.AggregateRequest_Aggregation_Number =
  exports.AggregateRequest_Aggregation_Integer =
  exports.AggregateRequest_Aggregation =
  exports.AggregateRequest =
  exports.protobufPackage =
    void 0;
/* eslint-disable */
const long_1 = __importDefault(require('long'));
const minimal_js_1 = __importDefault(require('protobufjs/minimal.js'));
const base_js_1 = require('./base.js');
const base_search_js_1 = require('./base_search.js');
exports.protobufPackage = 'weaviate.v1';
function createBaseAggregateRequest() {
  return {
    collection: '',
    tenant: '',
    objectsCount: false,
    aggregations: [],
    objectLimit: undefined,
    groupBy: undefined,
    limit: undefined,
    filters: undefined,
    hybrid: undefined,
    nearVector: undefined,
    nearObject: undefined,
    nearText: undefined,
    nearImage: undefined,
    nearAudio: undefined,
    nearVideo: undefined,
    nearDepth: undefined,
    nearThermal: undefined,
    nearImu: undefined,
  };
}
exports.AggregateRequest = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.collection !== '') {
      writer.uint32(10).string(message.collection);
    }
    if (message.tenant !== '') {
      writer.uint32(82).string(message.tenant);
    }
    if (message.objectsCount !== false) {
      writer.uint32(160).bool(message.objectsCount);
    }
    for (const v of message.aggregations) {
      exports.AggregateRequest_Aggregation.encode(v, writer.uint32(170).fork()).ldelim();
    }
    if (message.objectLimit !== undefined) {
      writer.uint32(240).uint32(message.objectLimit);
    }
    if (message.groupBy !== undefined) {
      exports.AggregateRequest_GroupBy.encode(message.groupBy, writer.uint32(250).fork()).ldelim();
    }
    if (message.limit !== undefined) {
      writer.uint32(256).uint32(message.limit);
    }
    if (message.filters !== undefined) {
      base_js_1.Filters.encode(message.filters, writer.uint32(322).fork()).ldelim();
    }
    if (message.hybrid !== undefined) {
      base_search_js_1.Hybrid.encode(message.hybrid, writer.uint32(330).fork()).ldelim();
    }
    if (message.nearVector !== undefined) {
      base_search_js_1.NearVector.encode(message.nearVector, writer.uint32(338).fork()).ldelim();
    }
    if (message.nearObject !== undefined) {
      base_search_js_1.NearObject.encode(message.nearObject, writer.uint32(346).fork()).ldelim();
    }
    if (message.nearText !== undefined) {
      base_search_js_1.NearTextSearch.encode(message.nearText, writer.uint32(354).fork()).ldelim();
    }
    if (message.nearImage !== undefined) {
      base_search_js_1.NearImageSearch.encode(message.nearImage, writer.uint32(362).fork()).ldelim();
    }
    if (message.nearAudio !== undefined) {
      base_search_js_1.NearAudioSearch.encode(message.nearAudio, writer.uint32(370).fork()).ldelim();
    }
    if (message.nearVideo !== undefined) {
      base_search_js_1.NearVideoSearch.encode(message.nearVideo, writer.uint32(378).fork()).ldelim();
    }
    if (message.nearDepth !== undefined) {
      base_search_js_1.NearDepthSearch.encode(message.nearDepth, writer.uint32(386).fork()).ldelim();
    }
    if (message.nearThermal !== undefined) {
      base_search_js_1.NearThermalSearch.encode(message.nearThermal, writer.uint32(394).fork()).ldelim();
    }
    if (message.nearImu !== undefined) {
      base_search_js_1.NearIMUSearch.encode(message.nearImu, writer.uint32(402).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.collection = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.tenant = reader.string();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }
          message.objectsCount = reader.bool();
          continue;
        case 21:
          if (tag !== 170) {
            break;
          }
          message.aggregations.push(exports.AggregateRequest_Aggregation.decode(reader, reader.uint32()));
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }
          message.objectLimit = reader.uint32();
          continue;
        case 31:
          if (tag !== 250) {
            break;
          }
          message.groupBy = exports.AggregateRequest_GroupBy.decode(reader, reader.uint32());
          continue;
        case 32:
          if (tag !== 256) {
            break;
          }
          message.limit = reader.uint32();
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }
          message.filters = base_js_1.Filters.decode(reader, reader.uint32());
          continue;
        case 41:
          if (tag !== 330) {
            break;
          }
          message.hybrid = base_search_js_1.Hybrid.decode(reader, reader.uint32());
          continue;
        case 42:
          if (tag !== 338) {
            break;
          }
          message.nearVector = base_search_js_1.NearVector.decode(reader, reader.uint32());
          continue;
        case 43:
          if (tag !== 346) {
            break;
          }
          message.nearObject = base_search_js_1.NearObject.decode(reader, reader.uint32());
          continue;
        case 44:
          if (tag !== 354) {
            break;
          }
          message.nearText = base_search_js_1.NearTextSearch.decode(reader, reader.uint32());
          continue;
        case 45:
          if (tag !== 362) {
            break;
          }
          message.nearImage = base_search_js_1.NearImageSearch.decode(reader, reader.uint32());
          continue;
        case 46:
          if (tag !== 370) {
            break;
          }
          message.nearAudio = base_search_js_1.NearAudioSearch.decode(reader, reader.uint32());
          continue;
        case 47:
          if (tag !== 378) {
            break;
          }
          message.nearVideo = base_search_js_1.NearVideoSearch.decode(reader, reader.uint32());
          continue;
        case 48:
          if (tag !== 386) {
            break;
          }
          message.nearDepth = base_search_js_1.NearDepthSearch.decode(reader, reader.uint32());
          continue;
        case 49:
          if (tag !== 394) {
            break;
          }
          message.nearThermal = base_search_js_1.NearThermalSearch.decode(reader, reader.uint32());
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }
          message.nearImu = base_search_js_1.NearIMUSearch.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      collection: isSet(object.collection) ? globalThis.String(object.collection) : '',
      tenant: isSet(object.tenant) ? globalThis.String(object.tenant) : '',
      objectsCount: isSet(object.objectsCount) ? globalThis.Boolean(object.objectsCount) : false,
      aggregations: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.aggregations
      )
        ? object.aggregations.map((e) => exports.AggregateRequest_Aggregation.fromJSON(e))
        : [],
      objectLimit: isSet(object.objectLimit) ? globalThis.Number(object.objectLimit) : undefined,
      groupBy: isSet(object.groupBy) ? exports.AggregateRequest_GroupBy.fromJSON(object.groupBy) : undefined,
      limit: isSet(object.limit) ? globalThis.Number(object.limit) : undefined,
      filters: isSet(object.filters) ? base_js_1.Filters.fromJSON(object.filters) : undefined,
      hybrid: isSet(object.hybrid) ? base_search_js_1.Hybrid.fromJSON(object.hybrid) : undefined,
      nearVector: isSet(object.nearVector)
        ? base_search_js_1.NearVector.fromJSON(object.nearVector)
        : undefined,
      nearObject: isSet(object.nearObject)
        ? base_search_js_1.NearObject.fromJSON(object.nearObject)
        : undefined,
      nearText: isSet(object.nearText)
        ? base_search_js_1.NearTextSearch.fromJSON(object.nearText)
        : undefined,
      nearImage: isSet(object.nearImage)
        ? base_search_js_1.NearImageSearch.fromJSON(object.nearImage)
        : undefined,
      nearAudio: isSet(object.nearAudio)
        ? base_search_js_1.NearAudioSearch.fromJSON(object.nearAudio)
        : undefined,
      nearVideo: isSet(object.nearVideo)
        ? base_search_js_1.NearVideoSearch.fromJSON(object.nearVideo)
        : undefined,
      nearDepth: isSet(object.nearDepth)
        ? base_search_js_1.NearDepthSearch.fromJSON(object.nearDepth)
        : undefined,
      nearThermal: isSet(object.nearThermal)
        ? base_search_js_1.NearThermalSearch.fromJSON(object.nearThermal)
        : undefined,
      nearImu: isSet(object.nearImu) ? base_search_js_1.NearIMUSearch.fromJSON(object.nearImu) : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.collection !== '') {
      obj.collection = message.collection;
    }
    if (message.tenant !== '') {
      obj.tenant = message.tenant;
    }
    if (message.objectsCount !== false) {
      obj.objectsCount = message.objectsCount;
    }
    if ((_a = message.aggregations) === null || _a === void 0 ? void 0 : _a.length) {
      obj.aggregations = message.aggregations.map((e) => exports.AggregateRequest_Aggregation.toJSON(e));
    }
    if (message.objectLimit !== undefined) {
      obj.objectLimit = Math.round(message.objectLimit);
    }
    if (message.groupBy !== undefined) {
      obj.groupBy = exports.AggregateRequest_GroupBy.toJSON(message.groupBy);
    }
    if (message.limit !== undefined) {
      obj.limit = Math.round(message.limit);
    }
    if (message.filters !== undefined) {
      obj.filters = base_js_1.Filters.toJSON(message.filters);
    }
    if (message.hybrid !== undefined) {
      obj.hybrid = base_search_js_1.Hybrid.toJSON(message.hybrid);
    }
    if (message.nearVector !== undefined) {
      obj.nearVector = base_search_js_1.NearVector.toJSON(message.nearVector);
    }
    if (message.nearObject !== undefined) {
      obj.nearObject = base_search_js_1.NearObject.toJSON(message.nearObject);
    }
    if (message.nearText !== undefined) {
      obj.nearText = base_search_js_1.NearTextSearch.toJSON(message.nearText);
    }
    if (message.nearImage !== undefined) {
      obj.nearImage = base_search_js_1.NearImageSearch.toJSON(message.nearImage);
    }
    if (message.nearAudio !== undefined) {
      obj.nearAudio = base_search_js_1.NearAudioSearch.toJSON(message.nearAudio);
    }
    if (message.nearVideo !== undefined) {
      obj.nearVideo = base_search_js_1.NearVideoSearch.toJSON(message.nearVideo);
    }
    if (message.nearDepth !== undefined) {
      obj.nearDepth = base_search_js_1.NearDepthSearch.toJSON(message.nearDepth);
    }
    if (message.nearThermal !== undefined) {
      obj.nearThermal = base_search_js_1.NearThermalSearch.toJSON(message.nearThermal);
    }
    if (message.nearImu !== undefined) {
      obj.nearImu = base_search_js_1.NearIMUSearch.toJSON(message.nearImu);
    }
    return obj;
  },
  create(base) {
    return exports.AggregateRequest.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f;
    const message = createBaseAggregateRequest();
    message.collection = (_a = object.collection) !== null && _a !== void 0 ? _a : '';
    message.tenant = (_b = object.tenant) !== null && _b !== void 0 ? _b : '';
    message.objectsCount = (_c = object.objectsCount) !== null && _c !== void 0 ? _c : false;
    message.aggregations =
      ((_d = object.aggregations) === null || _d === void 0
        ? void 0
        : _d.map((e) => exports.AggregateRequest_Aggregation.fromPartial(e))) || [];
    message.objectLimit = (_e = object.objectLimit) !== null && _e !== void 0 ? _e : undefined;
    message.groupBy =
      object.groupBy !== undefined && object.groupBy !== null
        ? exports.AggregateRequest_GroupBy.fromPartial(object.groupBy)
        : undefined;
    message.limit = (_f = object.limit) !== null && _f !== void 0 ? _f : undefined;
    message.filters =
      object.filters !== undefined && object.filters !== null
        ? base_js_1.Filters.fromPartial(object.filters)
        : undefined;
    message.hybrid =
      object.hybrid !== undefined && object.hybrid !== null
        ? base_search_js_1.Hybrid.fromPartial(object.hybrid)
        : undefined;
    message.nearVector =
      object.nearVector !== undefined && object.nearVector !== null
        ? base_search_js_1.NearVector.fromPartial(object.nearVector)
        : undefined;
    message.nearObject =
      object.nearObject !== undefined && object.nearObject !== null
        ? base_search_js_1.NearObject.fromPartial(object.nearObject)
        : undefined;
    message.nearText =
      object.nearText !== undefined && object.nearText !== null
        ? base_search_js_1.NearTextSearch.fromPartial(object.nearText)
        : undefined;
    message.nearImage =
      object.nearImage !== undefined && object.nearImage !== null
        ? base_search_js_1.NearImageSearch.fromPartial(object.nearImage)
        : undefined;
    message.nearAudio =
      object.nearAudio !== undefined && object.nearAudio !== null
        ? base_search_js_1.NearAudioSearch.fromPartial(object.nearAudio)
        : undefined;
    message.nearVideo =
      object.nearVideo !== undefined && object.nearVideo !== null
        ? base_search_js_1.NearVideoSearch.fromPartial(object.nearVideo)
        : undefined;
    message.nearDepth =
      object.nearDepth !== undefined && object.nearDepth !== null
        ? base_search_js_1.NearDepthSearch.fromPartial(object.nearDepth)
        : undefined;
    message.nearThermal =
      object.nearThermal !== undefined && object.nearThermal !== null
        ? base_search_js_1.NearThermalSearch.fromPartial(object.nearThermal)
        : undefined;
    message.nearImu =
      object.nearImu !== undefined && object.nearImu !== null
        ? base_search_js_1.NearIMUSearch.fromPartial(object.nearImu)
        : undefined;
    return message;
  },
};
function createBaseAggregateRequest_Aggregation() {
  return {
    property: '',
    int: undefined,
    number: undefined,
    text: undefined,
    boolean: undefined,
    date: undefined,
    reference: undefined,
  };
}
exports.AggregateRequest_Aggregation = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.property !== '') {
      writer.uint32(10).string(message.property);
    }
    if (message.int !== undefined) {
      exports.AggregateRequest_Aggregation_Integer.encode(message.int, writer.uint32(18).fork()).ldelim();
    }
    if (message.number !== undefined) {
      exports.AggregateRequest_Aggregation_Number.encode(message.number, writer.uint32(26).fork()).ldelim();
    }
    if (message.text !== undefined) {
      exports.AggregateRequest_Aggregation_Text.encode(message.text, writer.uint32(34).fork()).ldelim();
    }
    if (message.boolean !== undefined) {
      exports.AggregateRequest_Aggregation_Boolean.encode(message.boolean, writer.uint32(42).fork()).ldelim();
    }
    if (message.date !== undefined) {
      exports.AggregateRequest_Aggregation_DateMessage.encode(
        message.date,
        writer.uint32(50).fork()
      ).ldelim();
    }
    if (message.reference !== undefined) {
      exports.AggregateRequest_Aggregation_Reference.encode(
        message.reference,
        writer.uint32(58).fork()
      ).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateRequest_Aggregation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.property = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.int = exports.AggregateRequest_Aggregation_Integer.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.number = exports.AggregateRequest_Aggregation_Number.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.text = exports.AggregateRequest_Aggregation_Text.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.boolean = exports.AggregateRequest_Aggregation_Boolean.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.date = exports.AggregateRequest_Aggregation_DateMessage.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.reference = exports.AggregateRequest_Aggregation_Reference.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      property: isSet(object.property) ? globalThis.String(object.property) : '',
      int: isSet(object.int) ? exports.AggregateRequest_Aggregation_Integer.fromJSON(object.int) : undefined,
      number: isSet(object.number)
        ? exports.AggregateRequest_Aggregation_Number.fromJSON(object.number)
        : undefined,
      text: isSet(object.text) ? exports.AggregateRequest_Aggregation_Text.fromJSON(object.text) : undefined,
      boolean: isSet(object.boolean)
        ? exports.AggregateRequest_Aggregation_Boolean.fromJSON(object.boolean)
        : undefined,
      date: isSet(object.date)
        ? exports.AggregateRequest_Aggregation_DateMessage.fromJSON(object.date)
        : undefined,
      reference: isSet(object.reference)
        ? exports.AggregateRequest_Aggregation_Reference.fromJSON(object.reference)
        : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.property !== '') {
      obj.property = message.property;
    }
    if (message.int !== undefined) {
      obj.int = exports.AggregateRequest_Aggregation_Integer.toJSON(message.int);
    }
    if (message.number !== undefined) {
      obj.number = exports.AggregateRequest_Aggregation_Number.toJSON(message.number);
    }
    if (message.text !== undefined) {
      obj.text = exports.AggregateRequest_Aggregation_Text.toJSON(message.text);
    }
    if (message.boolean !== undefined) {
      obj.boolean = exports.AggregateRequest_Aggregation_Boolean.toJSON(message.boolean);
    }
    if (message.date !== undefined) {
      obj.date = exports.AggregateRequest_Aggregation_DateMessage.toJSON(message.date);
    }
    if (message.reference !== undefined) {
      obj.reference = exports.AggregateRequest_Aggregation_Reference.toJSON(message.reference);
    }
    return obj;
  },
  create(base) {
    return exports.AggregateRequest_Aggregation.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseAggregateRequest_Aggregation();
    message.property = (_a = object.property) !== null && _a !== void 0 ? _a : '';
    message.int =
      object.int !== undefined && object.int !== null
        ? exports.AggregateRequest_Aggregation_Integer.fromPartial(object.int)
        : undefined;
    message.number =
      object.number !== undefined && object.number !== null
        ? exports.AggregateRequest_Aggregation_Number.fromPartial(object.number)
        : undefined;
    message.text =
      object.text !== undefined && object.text !== null
        ? exports.AggregateRequest_Aggregation_Text.fromPartial(object.text)
        : undefined;
    message.boolean =
      object.boolean !== undefined && object.boolean !== null
        ? exports.AggregateRequest_Aggregation_Boolean.fromPartial(object.boolean)
        : undefined;
    message.date =
      object.date !== undefined && object.date !== null
        ? exports.AggregateRequest_Aggregation_DateMessage.fromPartial(object.date)
        : undefined;
    message.reference =
      object.reference !== undefined && object.reference !== null
        ? exports.AggregateRequest_Aggregation_Reference.fromPartial(object.reference)
        : undefined;
    return message;
  },
};
function createBaseAggregateRequest_Aggregation_Integer() {
  return {
    count: false,
    type: false,
    sum: false,
    mean: false,
    mode: false,
    median: false,
    maximum: false,
    minimum: false,
  };
}
exports.AggregateRequest_Aggregation_Integer = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.count !== false) {
      writer.uint32(8).bool(message.count);
    }
    if (message.type !== false) {
      writer.uint32(16).bool(message.type);
    }
    if (message.sum !== false) {
      writer.uint32(24).bool(message.sum);
    }
    if (message.mean !== false) {
      writer.uint32(32).bool(message.mean);
    }
    if (message.mode !== false) {
      writer.uint32(40).bool(message.mode);
    }
    if (message.median !== false) {
      writer.uint32(48).bool(message.median);
    }
    if (message.maximum !== false) {
      writer.uint32(56).bool(message.maximum);
    }
    if (message.minimum !== false) {
      writer.uint32(64).bool(message.minimum);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateRequest_Aggregation_Integer();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.count = reader.bool();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.type = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.sum = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.mean = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.mode = reader.bool();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }
          message.median = reader.bool();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }
          message.maximum = reader.bool();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }
          message.minimum = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      count: isSet(object.count) ? globalThis.Boolean(object.count) : false,
      type: isSet(object.type) ? globalThis.Boolean(object.type) : false,
      sum: isSet(object.sum) ? globalThis.Boolean(object.sum) : false,
      mean: isSet(object.mean) ? globalThis.Boolean(object.mean) : false,
      mode: isSet(object.mode) ? globalThis.Boolean(object.mode) : false,
      median: isSet(object.median) ? globalThis.Boolean(object.median) : false,
      maximum: isSet(object.maximum) ? globalThis.Boolean(object.maximum) : false,
      minimum: isSet(object.minimum) ? globalThis.Boolean(object.minimum) : false,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.count !== false) {
      obj.count = message.count;
    }
    if (message.type !== false) {
      obj.type = message.type;
    }
    if (message.sum !== false) {
      obj.sum = message.sum;
    }
    if (message.mean !== false) {
      obj.mean = message.mean;
    }
    if (message.mode !== false) {
      obj.mode = message.mode;
    }
    if (message.median !== false) {
      obj.median = message.median;
    }
    if (message.maximum !== false) {
      obj.maximum = message.maximum;
    }
    if (message.minimum !== false) {
      obj.minimum = message.minimum;
    }
    return obj;
  },
  create(base) {
    return exports.AggregateRequest_Aggregation_Integer.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    const message = createBaseAggregateRequest_Aggregation_Integer();
    message.count = (_a = object.count) !== null && _a !== void 0 ? _a : false;
    message.type = (_b = object.type) !== null && _b !== void 0 ? _b : false;
    message.sum = (_c = object.sum) !== null && _c !== void 0 ? _c : false;
    message.mean = (_d = object.mean) !== null && _d !== void 0 ? _d : false;
    message.mode = (_e = object.mode) !== null && _e !== void 0 ? _e : false;
    message.median = (_f = object.median) !== null && _f !== void 0 ? _f : false;
    message.maximum = (_g = object.maximum) !== null && _g !== void 0 ? _g : false;
    message.minimum = (_h = object.minimum) !== null && _h !== void 0 ? _h : false;
    return message;
  },
};
function createBaseAggregateRequest_Aggregation_Number() {
  return {
    count: false,
    type: false,
    sum: false,
    mean: false,
    mode: false,
    median: false,
    maximum: false,
    minimum: false,
  };
}
exports.AggregateRequest_Aggregation_Number = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.count !== false) {
      writer.uint32(8).bool(message.count);
    }
    if (message.type !== false) {
      writer.uint32(16).bool(message.type);
    }
    if (message.sum !== false) {
      writer.uint32(24).bool(message.sum);
    }
    if (message.mean !== false) {
      writer.uint32(32).bool(message.mean);
    }
    if (message.mode !== false) {
      writer.uint32(40).bool(message.mode);
    }
    if (message.median !== false) {
      writer.uint32(48).bool(message.median);
    }
    if (message.maximum !== false) {
      writer.uint32(56).bool(message.maximum);
    }
    if (message.minimum !== false) {
      writer.uint32(64).bool(message.minimum);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateRequest_Aggregation_Number();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.count = reader.bool();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.type = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.sum = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.mean = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.mode = reader.bool();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }
          message.median = reader.bool();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }
          message.maximum = reader.bool();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }
          message.minimum = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      count: isSet(object.count) ? globalThis.Boolean(object.count) : false,
      type: isSet(object.type) ? globalThis.Boolean(object.type) : false,
      sum: isSet(object.sum) ? globalThis.Boolean(object.sum) : false,
      mean: isSet(object.mean) ? globalThis.Boolean(object.mean) : false,
      mode: isSet(object.mode) ? globalThis.Boolean(object.mode) : false,
      median: isSet(object.median) ? globalThis.Boolean(object.median) : false,
      maximum: isSet(object.maximum) ? globalThis.Boolean(object.maximum) : false,
      minimum: isSet(object.minimum) ? globalThis.Boolean(object.minimum) : false,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.count !== false) {
      obj.count = message.count;
    }
    if (message.type !== false) {
      obj.type = message.type;
    }
    if (message.sum !== false) {
      obj.sum = message.sum;
    }
    if (message.mean !== false) {
      obj.mean = message.mean;
    }
    if (message.mode !== false) {
      obj.mode = message.mode;
    }
    if (message.median !== false) {
      obj.median = message.median;
    }
    if (message.maximum !== false) {
      obj.maximum = message.maximum;
    }
    if (message.minimum !== false) {
      obj.minimum = message.minimum;
    }
    return obj;
  },
  create(base) {
    return exports.AggregateRequest_Aggregation_Number.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    const message = createBaseAggregateRequest_Aggregation_Number();
    message.count = (_a = object.count) !== null && _a !== void 0 ? _a : false;
    message.type = (_b = object.type) !== null && _b !== void 0 ? _b : false;
    message.sum = (_c = object.sum) !== null && _c !== void 0 ? _c : false;
    message.mean = (_d = object.mean) !== null && _d !== void 0 ? _d : false;
    message.mode = (_e = object.mode) !== null && _e !== void 0 ? _e : false;
    message.median = (_f = object.median) !== null && _f !== void 0 ? _f : false;
    message.maximum = (_g = object.maximum) !== null && _g !== void 0 ? _g : false;
    message.minimum = (_h = object.minimum) !== null && _h !== void 0 ? _h : false;
    return message;
  },
};
function createBaseAggregateRequest_Aggregation_Text() {
  return { count: false, type: false, topOccurences: false, topOccurencesLimit: undefined };
}
exports.AggregateRequest_Aggregation_Text = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.count !== false) {
      writer.uint32(8).bool(message.count);
    }
    if (message.type !== false) {
      writer.uint32(16).bool(message.type);
    }
    if (message.topOccurences !== false) {
      writer.uint32(24).bool(message.topOccurences);
    }
    if (message.topOccurencesLimit !== undefined) {
      writer.uint32(32).uint32(message.topOccurencesLimit);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateRequest_Aggregation_Text();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.count = reader.bool();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.type = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.topOccurences = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.topOccurencesLimit = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      count: isSet(object.count) ? globalThis.Boolean(object.count) : false,
      type: isSet(object.type) ? globalThis.Boolean(object.type) : false,
      topOccurences: isSet(object.topOccurences) ? globalThis.Boolean(object.topOccurences) : false,
      topOccurencesLimit: isSet(object.topOccurencesLimit)
        ? globalThis.Number(object.topOccurencesLimit)
        : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.count !== false) {
      obj.count = message.count;
    }
    if (message.type !== false) {
      obj.type = message.type;
    }
    if (message.topOccurences !== false) {
      obj.topOccurences = message.topOccurences;
    }
    if (message.topOccurencesLimit !== undefined) {
      obj.topOccurencesLimit = Math.round(message.topOccurencesLimit);
    }
    return obj;
  },
  create(base) {
    return exports.AggregateRequest_Aggregation_Text.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseAggregateRequest_Aggregation_Text();
    message.count = (_a = object.count) !== null && _a !== void 0 ? _a : false;
    message.type = (_b = object.type) !== null && _b !== void 0 ? _b : false;
    message.topOccurences = (_c = object.topOccurences) !== null && _c !== void 0 ? _c : false;
    message.topOccurencesLimit = (_d = object.topOccurencesLimit) !== null && _d !== void 0 ? _d : undefined;
    return message;
  },
};
function createBaseAggregateRequest_Aggregation_Boolean() {
  return {
    count: false,
    type: false,
    totalTrue: false,
    totalFalse: false,
    percentageTrue: false,
    percentageFalse: false,
  };
}
exports.AggregateRequest_Aggregation_Boolean = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.count !== false) {
      writer.uint32(8).bool(message.count);
    }
    if (message.type !== false) {
      writer.uint32(16).bool(message.type);
    }
    if (message.totalTrue !== false) {
      writer.uint32(24).bool(message.totalTrue);
    }
    if (message.totalFalse !== false) {
      writer.uint32(32).bool(message.totalFalse);
    }
    if (message.percentageTrue !== false) {
      writer.uint32(40).bool(message.percentageTrue);
    }
    if (message.percentageFalse !== false) {
      writer.uint32(48).bool(message.percentageFalse);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateRequest_Aggregation_Boolean();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.count = reader.bool();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.type = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.totalTrue = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.totalFalse = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.percentageTrue = reader.bool();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }
          message.percentageFalse = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      count: isSet(object.count) ? globalThis.Boolean(object.count) : false,
      type: isSet(object.type) ? globalThis.Boolean(object.type) : false,
      totalTrue: isSet(object.totalTrue) ? globalThis.Boolean(object.totalTrue) : false,
      totalFalse: isSet(object.totalFalse) ? globalThis.Boolean(object.totalFalse) : false,
      percentageTrue: isSet(object.percentageTrue) ? globalThis.Boolean(object.percentageTrue) : false,
      percentageFalse: isSet(object.percentageFalse) ? globalThis.Boolean(object.percentageFalse) : false,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.count !== false) {
      obj.count = message.count;
    }
    if (message.type !== false) {
      obj.type = message.type;
    }
    if (message.totalTrue !== false) {
      obj.totalTrue = message.totalTrue;
    }
    if (message.totalFalse !== false) {
      obj.totalFalse = message.totalFalse;
    }
    if (message.percentageTrue !== false) {
      obj.percentageTrue = message.percentageTrue;
    }
    if (message.percentageFalse !== false) {
      obj.percentageFalse = message.percentageFalse;
    }
    return obj;
  },
  create(base) {
    return exports.AggregateRequest_Aggregation_Boolean.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f;
    const message = createBaseAggregateRequest_Aggregation_Boolean();
    message.count = (_a = object.count) !== null && _a !== void 0 ? _a : false;
    message.type = (_b = object.type) !== null && _b !== void 0 ? _b : false;
    message.totalTrue = (_c = object.totalTrue) !== null && _c !== void 0 ? _c : false;
    message.totalFalse = (_d = object.totalFalse) !== null && _d !== void 0 ? _d : false;
    message.percentageTrue = (_e = object.percentageTrue) !== null && _e !== void 0 ? _e : false;
    message.percentageFalse = (_f = object.percentageFalse) !== null && _f !== void 0 ? _f : false;
    return message;
  },
};
function createBaseAggregateRequest_Aggregation_DateMessage() {
  return { count: false, type: false, median: false, mode: false, maximum: false, minimum: false };
}
exports.AggregateRequest_Aggregation_DateMessage = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.count !== false) {
      writer.uint32(8).bool(message.count);
    }
    if (message.type !== false) {
      writer.uint32(16).bool(message.type);
    }
    if (message.median !== false) {
      writer.uint32(24).bool(message.median);
    }
    if (message.mode !== false) {
      writer.uint32(32).bool(message.mode);
    }
    if (message.maximum !== false) {
      writer.uint32(40).bool(message.maximum);
    }
    if (message.minimum !== false) {
      writer.uint32(48).bool(message.minimum);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateRequest_Aggregation_DateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.count = reader.bool();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.type = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.median = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.mode = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.maximum = reader.bool();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }
          message.minimum = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      count: isSet(object.count) ? globalThis.Boolean(object.count) : false,
      type: isSet(object.type) ? globalThis.Boolean(object.type) : false,
      median: isSet(object.median) ? globalThis.Boolean(object.median) : false,
      mode: isSet(object.mode) ? globalThis.Boolean(object.mode) : false,
      maximum: isSet(object.maximum) ? globalThis.Boolean(object.maximum) : false,
      minimum: isSet(object.minimum) ? globalThis.Boolean(object.minimum) : false,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.count !== false) {
      obj.count = message.count;
    }
    if (message.type !== false) {
      obj.type = message.type;
    }
    if (message.median !== false) {
      obj.median = message.median;
    }
    if (message.mode !== false) {
      obj.mode = message.mode;
    }
    if (message.maximum !== false) {
      obj.maximum = message.maximum;
    }
    if (message.minimum !== false) {
      obj.minimum = message.minimum;
    }
    return obj;
  },
  create(base) {
    return exports.AggregateRequest_Aggregation_DateMessage.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f;
    const message = createBaseAggregateRequest_Aggregation_DateMessage();
    message.count = (_a = object.count) !== null && _a !== void 0 ? _a : false;
    message.type = (_b = object.type) !== null && _b !== void 0 ? _b : false;
    message.median = (_c = object.median) !== null && _c !== void 0 ? _c : false;
    message.mode = (_d = object.mode) !== null && _d !== void 0 ? _d : false;
    message.maximum = (_e = object.maximum) !== null && _e !== void 0 ? _e : false;
    message.minimum = (_f = object.minimum) !== null && _f !== void 0 ? _f : false;
    return message;
  },
};
function createBaseAggregateRequest_Aggregation_Reference() {
  return { type: false, pointingTo: false };
}
exports.AggregateRequest_Aggregation_Reference = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.type !== false) {
      writer.uint32(8).bool(message.type);
    }
    if (message.pointingTo !== false) {
      writer.uint32(16).bool(message.pointingTo);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateRequest_Aggregation_Reference();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.type = reader.bool();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.pointingTo = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      type: isSet(object.type) ? globalThis.Boolean(object.type) : false,
      pointingTo: isSet(object.pointingTo) ? globalThis.Boolean(object.pointingTo) : false,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.type !== false) {
      obj.type = message.type;
    }
    if (message.pointingTo !== false) {
      obj.pointingTo = message.pointingTo;
    }
    return obj;
  },
  create(base) {
    return exports.AggregateRequest_Aggregation_Reference.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseAggregateRequest_Aggregation_Reference();
    message.type = (_a = object.type) !== null && _a !== void 0 ? _a : false;
    message.pointingTo = (_b = object.pointingTo) !== null && _b !== void 0 ? _b : false;
    return message;
  },
};
function createBaseAggregateRequest_GroupBy() {
  return { collection: '', property: '' };
}
exports.AggregateRequest_GroupBy = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.collection !== '') {
      writer.uint32(10).string(message.collection);
    }
    if (message.property !== '') {
      writer.uint32(18).string(message.property);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateRequest_GroupBy();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.collection = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.property = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      collection: isSet(object.collection) ? globalThis.String(object.collection) : '',
      property: isSet(object.property) ? globalThis.String(object.property) : '',
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.collection !== '') {
      obj.collection = message.collection;
    }
    if (message.property !== '') {
      obj.property = message.property;
    }
    return obj;
  },
  create(base) {
    return exports.AggregateRequest_GroupBy.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseAggregateRequest_GroupBy();
    message.collection = (_a = object.collection) !== null && _a !== void 0 ? _a : '';
    message.property = (_b = object.property) !== null && _b !== void 0 ? _b : '';
    return message;
  },
};
function createBaseAggregateReply() {
  return { took: 0, singleResult: undefined, groupedResults: undefined };
}
exports.AggregateReply = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.took !== 0) {
      writer.uint32(13).float(message.took);
    }
    if (message.singleResult !== undefined) {
      exports.AggregateReply_Single.encode(message.singleResult, writer.uint32(18).fork()).ldelim();
    }
    if (message.groupedResults !== undefined) {
      exports.AggregateReply_Grouped.encode(message.groupedResults, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 13) {
            break;
          }
          message.took = reader.float();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.singleResult = exports.AggregateReply_Single.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.groupedResults = exports.AggregateReply_Grouped.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      took: isSet(object.took) ? globalThis.Number(object.took) : 0,
      singleResult: isSet(object.singleResult)
        ? exports.AggregateReply_Single.fromJSON(object.singleResult)
        : undefined,
      groupedResults: isSet(object.groupedResults)
        ? exports.AggregateReply_Grouped.fromJSON(object.groupedResults)
        : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.took !== 0) {
      obj.took = message.took;
    }
    if (message.singleResult !== undefined) {
      obj.singleResult = exports.AggregateReply_Single.toJSON(message.singleResult);
    }
    if (message.groupedResults !== undefined) {
      obj.groupedResults = exports.AggregateReply_Grouped.toJSON(message.groupedResults);
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseAggregateReply();
    message.took = (_a = object.took) !== null && _a !== void 0 ? _a : 0;
    message.singleResult =
      object.singleResult !== undefined && object.singleResult !== null
        ? exports.AggregateReply_Single.fromPartial(object.singleResult)
        : undefined;
    message.groupedResults =
      object.groupedResults !== undefined && object.groupedResults !== null
        ? exports.AggregateReply_Grouped.fromPartial(object.groupedResults)
        : undefined;
    return message;
  },
};
function createBaseAggregateReply_Aggregations() {
  return { aggregations: [] };
}
exports.AggregateReply_Aggregations = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.aggregations) {
      exports.AggregateReply_Aggregations_Aggregation.encode(v, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Aggregations();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.aggregations.push(
            exports.AggregateReply_Aggregations_Aggregation.decode(reader, reader.uint32())
          );
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      aggregations: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.aggregations
      )
        ? object.aggregations.map((e) => exports.AggregateReply_Aggregations_Aggregation.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.aggregations) === null || _a === void 0 ? void 0 : _a.length) {
      obj.aggregations = message.aggregations.map((e) =>
        exports.AggregateReply_Aggregations_Aggregation.toJSON(e)
      );
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Aggregations.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseAggregateReply_Aggregations();
    message.aggregations =
      ((_a = object.aggregations) === null || _a === void 0
        ? void 0
        : _a.map((e) => exports.AggregateReply_Aggregations_Aggregation.fromPartial(e))) || [];
    return message;
  },
};
function createBaseAggregateReply_Aggregations_Aggregation() {
  return {
    property: '',
    int: undefined,
    number: undefined,
    text: undefined,
    boolean: undefined,
    date: undefined,
    reference: undefined,
  };
}
exports.AggregateReply_Aggregations_Aggregation = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.property !== '') {
      writer.uint32(10).string(message.property);
    }
    if (message.int !== undefined) {
      exports.AggregateReply_Aggregations_Aggregation_Integer.encode(
        message.int,
        writer.uint32(18).fork()
      ).ldelim();
    }
    if (message.number !== undefined) {
      exports.AggregateReply_Aggregations_Aggregation_Number.encode(
        message.number,
        writer.uint32(26).fork()
      ).ldelim();
    }
    if (message.text !== undefined) {
      exports.AggregateReply_Aggregations_Aggregation_Text.encode(
        message.text,
        writer.uint32(34).fork()
      ).ldelim();
    }
    if (message.boolean !== undefined) {
      exports.AggregateReply_Aggregations_Aggregation_Boolean.encode(
        message.boolean,
        writer.uint32(42).fork()
      ).ldelim();
    }
    if (message.date !== undefined) {
      exports.AggregateReply_Aggregations_Aggregation_DateMessage.encode(
        message.date,
        writer.uint32(50).fork()
      ).ldelim();
    }
    if (message.reference !== undefined) {
      exports.AggregateReply_Aggregations_Aggregation_Reference.encode(
        message.reference,
        writer.uint32(58).fork()
      ).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Aggregations_Aggregation();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.property = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.int = exports.AggregateReply_Aggregations_Aggregation_Integer.decode(
            reader,
            reader.uint32()
          );
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.number = exports.AggregateReply_Aggregations_Aggregation_Number.decode(
            reader,
            reader.uint32()
          );
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.text = exports.AggregateReply_Aggregations_Aggregation_Text.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.boolean = exports.AggregateReply_Aggregations_Aggregation_Boolean.decode(
            reader,
            reader.uint32()
          );
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.date = exports.AggregateReply_Aggregations_Aggregation_DateMessage.decode(
            reader,
            reader.uint32()
          );
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.reference = exports.AggregateReply_Aggregations_Aggregation_Reference.decode(
            reader,
            reader.uint32()
          );
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      property: isSet(object.property) ? globalThis.String(object.property) : '',
      int: isSet(object.int)
        ? exports.AggregateReply_Aggregations_Aggregation_Integer.fromJSON(object.int)
        : undefined,
      number: isSet(object.number)
        ? exports.AggregateReply_Aggregations_Aggregation_Number.fromJSON(object.number)
        : undefined,
      text: isSet(object.text)
        ? exports.AggregateReply_Aggregations_Aggregation_Text.fromJSON(object.text)
        : undefined,
      boolean: isSet(object.boolean)
        ? exports.AggregateReply_Aggregations_Aggregation_Boolean.fromJSON(object.boolean)
        : undefined,
      date: isSet(object.date)
        ? exports.AggregateReply_Aggregations_Aggregation_DateMessage.fromJSON(object.date)
        : undefined,
      reference: isSet(object.reference)
        ? exports.AggregateReply_Aggregations_Aggregation_Reference.fromJSON(object.reference)
        : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.property !== '') {
      obj.property = message.property;
    }
    if (message.int !== undefined) {
      obj.int = exports.AggregateReply_Aggregations_Aggregation_Integer.toJSON(message.int);
    }
    if (message.number !== undefined) {
      obj.number = exports.AggregateReply_Aggregations_Aggregation_Number.toJSON(message.number);
    }
    if (message.text !== undefined) {
      obj.text = exports.AggregateReply_Aggregations_Aggregation_Text.toJSON(message.text);
    }
    if (message.boolean !== undefined) {
      obj.boolean = exports.AggregateReply_Aggregations_Aggregation_Boolean.toJSON(message.boolean);
    }
    if (message.date !== undefined) {
      obj.date = exports.AggregateReply_Aggregations_Aggregation_DateMessage.toJSON(message.date);
    }
    if (message.reference !== undefined) {
      obj.reference = exports.AggregateReply_Aggregations_Aggregation_Reference.toJSON(message.reference);
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Aggregations_Aggregation.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a;
    const message = createBaseAggregateReply_Aggregations_Aggregation();
    message.property = (_a = object.property) !== null && _a !== void 0 ? _a : '';
    message.int =
      object.int !== undefined && object.int !== null
        ? exports.AggregateReply_Aggregations_Aggregation_Integer.fromPartial(object.int)
        : undefined;
    message.number =
      object.number !== undefined && object.number !== null
        ? exports.AggregateReply_Aggregations_Aggregation_Number.fromPartial(object.number)
        : undefined;
    message.text =
      object.text !== undefined && object.text !== null
        ? exports.AggregateReply_Aggregations_Aggregation_Text.fromPartial(object.text)
        : undefined;
    message.boolean =
      object.boolean !== undefined && object.boolean !== null
        ? exports.AggregateReply_Aggregations_Aggregation_Boolean.fromPartial(object.boolean)
        : undefined;
    message.date =
      object.date !== undefined && object.date !== null
        ? exports.AggregateReply_Aggregations_Aggregation_DateMessage.fromPartial(object.date)
        : undefined;
    message.reference =
      object.reference !== undefined && object.reference !== null
        ? exports.AggregateReply_Aggregations_Aggregation_Reference.fromPartial(object.reference)
        : undefined;
    return message;
  },
};
function createBaseAggregateReply_Aggregations_Aggregation_Integer() {
  return {
    count: undefined,
    type: undefined,
    mean: undefined,
    median: undefined,
    mode: undefined,
    maximum: undefined,
    minimum: undefined,
    sum: undefined,
  };
}
exports.AggregateReply_Aggregations_Aggregation_Integer = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.count !== undefined) {
      writer.uint32(8).int64(message.count);
    }
    if (message.type !== undefined) {
      writer.uint32(18).string(message.type);
    }
    if (message.mean !== undefined) {
      writer.uint32(25).double(message.mean);
    }
    if (message.median !== undefined) {
      writer.uint32(33).double(message.median);
    }
    if (message.mode !== undefined) {
      writer.uint32(40).int64(message.mode);
    }
    if (message.maximum !== undefined) {
      writer.uint32(48).int64(message.maximum);
    }
    if (message.minimum !== undefined) {
      writer.uint32(56).int64(message.minimum);
    }
    if (message.sum !== undefined) {
      writer.uint32(64).int64(message.sum);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Integer();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.count = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.type = reader.string();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.mean = reader.double();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }
          message.median = reader.double();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.mode = longToNumber(reader.int64());
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }
          message.maximum = longToNumber(reader.int64());
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }
          message.minimum = longToNumber(reader.int64());
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }
          message.sum = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      count: isSet(object.count) ? globalThis.Number(object.count) : undefined,
      type: isSet(object.type) ? globalThis.String(object.type) : undefined,
      mean: isSet(object.mean) ? globalThis.Number(object.mean) : undefined,
      median: isSet(object.median) ? globalThis.Number(object.median) : undefined,
      mode: isSet(object.mode) ? globalThis.Number(object.mode) : undefined,
      maximum: isSet(object.maximum) ? globalThis.Number(object.maximum) : undefined,
      minimum: isSet(object.minimum) ? globalThis.Number(object.minimum) : undefined,
      sum: isSet(object.sum) ? globalThis.Number(object.sum) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.count !== undefined) {
      obj.count = Math.round(message.count);
    }
    if (message.type !== undefined) {
      obj.type = message.type;
    }
    if (message.mean !== undefined) {
      obj.mean = message.mean;
    }
    if (message.median !== undefined) {
      obj.median = message.median;
    }
    if (message.mode !== undefined) {
      obj.mode = Math.round(message.mode);
    }
    if (message.maximum !== undefined) {
      obj.maximum = Math.round(message.maximum);
    }
    if (message.minimum !== undefined) {
      obj.minimum = Math.round(message.minimum);
    }
    if (message.sum !== undefined) {
      obj.sum = Math.round(message.sum);
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Aggregations_Aggregation_Integer.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Integer();
    message.count = (_a = object.count) !== null && _a !== void 0 ? _a : undefined;
    message.type = (_b = object.type) !== null && _b !== void 0 ? _b : undefined;
    message.mean = (_c = object.mean) !== null && _c !== void 0 ? _c : undefined;
    message.median = (_d = object.median) !== null && _d !== void 0 ? _d : undefined;
    message.mode = (_e = object.mode) !== null && _e !== void 0 ? _e : undefined;
    message.maximum = (_f = object.maximum) !== null && _f !== void 0 ? _f : undefined;
    message.minimum = (_g = object.minimum) !== null && _g !== void 0 ? _g : undefined;
    message.sum = (_h = object.sum) !== null && _h !== void 0 ? _h : undefined;
    return message;
  },
};
function createBaseAggregateReply_Aggregations_Aggregation_Number() {
  return {
    count: undefined,
    type: undefined,
    mean: undefined,
    median: undefined,
    mode: undefined,
    maximum: undefined,
    minimum: undefined,
    sum: undefined,
  };
}
exports.AggregateReply_Aggregations_Aggregation_Number = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.count !== undefined) {
      writer.uint32(8).int64(message.count);
    }
    if (message.type !== undefined) {
      writer.uint32(18).string(message.type);
    }
    if (message.mean !== undefined) {
      writer.uint32(25).double(message.mean);
    }
    if (message.median !== undefined) {
      writer.uint32(33).double(message.median);
    }
    if (message.mode !== undefined) {
      writer.uint32(41).double(message.mode);
    }
    if (message.maximum !== undefined) {
      writer.uint32(49).double(message.maximum);
    }
    if (message.minimum !== undefined) {
      writer.uint32(57).double(message.minimum);
    }
    if (message.sum !== undefined) {
      writer.uint32(65).double(message.sum);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Number();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.count = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.type = reader.string();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.mean = reader.double();
          continue;
        case 4:
          if (tag !== 33) {
            break;
          }
          message.median = reader.double();
          continue;
        case 5:
          if (tag !== 41) {
            break;
          }
          message.mode = reader.double();
          continue;
        case 6:
          if (tag !== 49) {
            break;
          }
          message.maximum = reader.double();
          continue;
        case 7:
          if (tag !== 57) {
            break;
          }
          message.minimum = reader.double();
          continue;
        case 8:
          if (tag !== 65) {
            break;
          }
          message.sum = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      count: isSet(object.count) ? globalThis.Number(object.count) : undefined,
      type: isSet(object.type) ? globalThis.String(object.type) : undefined,
      mean: isSet(object.mean) ? globalThis.Number(object.mean) : undefined,
      median: isSet(object.median) ? globalThis.Number(object.median) : undefined,
      mode: isSet(object.mode) ? globalThis.Number(object.mode) : undefined,
      maximum: isSet(object.maximum) ? globalThis.Number(object.maximum) : undefined,
      minimum: isSet(object.minimum) ? globalThis.Number(object.minimum) : undefined,
      sum: isSet(object.sum) ? globalThis.Number(object.sum) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.count !== undefined) {
      obj.count = Math.round(message.count);
    }
    if (message.type !== undefined) {
      obj.type = message.type;
    }
    if (message.mean !== undefined) {
      obj.mean = message.mean;
    }
    if (message.median !== undefined) {
      obj.median = message.median;
    }
    if (message.mode !== undefined) {
      obj.mode = message.mode;
    }
    if (message.maximum !== undefined) {
      obj.maximum = message.maximum;
    }
    if (message.minimum !== undefined) {
      obj.minimum = message.minimum;
    }
    if (message.sum !== undefined) {
      obj.sum = message.sum;
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Aggregations_Aggregation_Number.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Number();
    message.count = (_a = object.count) !== null && _a !== void 0 ? _a : undefined;
    message.type = (_b = object.type) !== null && _b !== void 0 ? _b : undefined;
    message.mean = (_c = object.mean) !== null && _c !== void 0 ? _c : undefined;
    message.median = (_d = object.median) !== null && _d !== void 0 ? _d : undefined;
    message.mode = (_e = object.mode) !== null && _e !== void 0 ? _e : undefined;
    message.maximum = (_f = object.maximum) !== null && _f !== void 0 ? _f : undefined;
    message.minimum = (_g = object.minimum) !== null && _g !== void 0 ? _g : undefined;
    message.sum = (_h = object.sum) !== null && _h !== void 0 ? _h : undefined;
    return message;
  },
};
function createBaseAggregateReply_Aggregations_Aggregation_Text() {
  return { count: undefined, type: undefined, topOccurences: undefined };
}
exports.AggregateReply_Aggregations_Aggregation_Text = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.count !== undefined) {
      writer.uint32(8).int64(message.count);
    }
    if (message.type !== undefined) {
      writer.uint32(18).string(message.type);
    }
    if (message.topOccurences !== undefined) {
      exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences.encode(
        message.topOccurences,
        writer.uint32(26).fork()
      ).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Text();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.count = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.type = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.topOccurences = exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences.decode(
            reader,
            reader.uint32()
          );
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      count: isSet(object.count) ? globalThis.Number(object.count) : undefined,
      type: isSet(object.type) ? globalThis.String(object.type) : undefined,
      topOccurences: isSet(object.topOccurences)
        ? exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences.fromJSON(object.topOccurences)
        : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.count !== undefined) {
      obj.count = Math.round(message.count);
    }
    if (message.type !== undefined) {
      obj.type = message.type;
    }
    if (message.topOccurences !== undefined) {
      obj.topOccurences = exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences.toJSON(
        message.topOccurences
      );
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Aggregations_Aggregation_Text.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Text();
    message.count = (_a = object.count) !== null && _a !== void 0 ? _a : undefined;
    message.type = (_b = object.type) !== null && _b !== void 0 ? _b : undefined;
    message.topOccurences =
      object.topOccurences !== undefined && object.topOccurences !== null
        ? exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences.fromPartial(
            object.topOccurences
          )
        : undefined;
    return message;
  },
};
function createBaseAggregateReply_Aggregations_Aggregation_Text_TopOccurrences() {
  return { items: [] };
}
exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.items) {
      exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences_TopOccurrence.encode(
        v,
        writer.uint32(10).fork()
      ).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Text_TopOccurrences();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.items.push(
            exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences_TopOccurrence.decode(
              reader,
              reader.uint32()
            )
          );
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      items: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.items)
        ? object.items.map((e) =>
            exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences_TopOccurrence.fromJSON(e)
          )
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.items) === null || _a === void 0 ? void 0 : _a.length) {
      obj.items = message.items.map((e) =>
        exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences_TopOccurrence.toJSON(e)
      );
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Text_TopOccurrences();
    message.items =
      ((_a = object.items) === null || _a === void 0
        ? void 0
        : _a.map((e) =>
            exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences_TopOccurrence.fromPartial(e)
          )) || [];
    return message;
  },
};
function createBaseAggregateReply_Aggregations_Aggregation_Text_TopOccurrences_TopOccurrence() {
  return { value: '', occurs: 0 };
}
exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences_TopOccurrence = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.value !== '') {
      writer.uint32(10).string(message.value);
    }
    if (message.occurs !== 0) {
      writer.uint32(16).int64(message.occurs);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Text_TopOccurrences_TopOccurrence();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.value = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.occurs = longToNumber(reader.int64());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      value: isSet(object.value) ? globalThis.String(object.value) : '',
      occurs: isSet(object.occurs) ? globalThis.Number(object.occurs) : 0,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.value !== '') {
      obj.value = message.value;
    }
    if (message.occurs !== 0) {
      obj.occurs = Math.round(message.occurs);
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Aggregations_Aggregation_Text_TopOccurrences_TopOccurrence.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Text_TopOccurrences_TopOccurrence();
    message.value = (_a = object.value) !== null && _a !== void 0 ? _a : '';
    message.occurs = (_b = object.occurs) !== null && _b !== void 0 ? _b : 0;
    return message;
  },
};
function createBaseAggregateReply_Aggregations_Aggregation_Boolean() {
  return {
    count: undefined,
    type: undefined,
    totalTrue: undefined,
    totalFalse: undefined,
    percentageTrue: undefined,
    percentageFalse: undefined,
  };
}
exports.AggregateReply_Aggregations_Aggregation_Boolean = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.count !== undefined) {
      writer.uint32(8).int64(message.count);
    }
    if (message.type !== undefined) {
      writer.uint32(18).string(message.type);
    }
    if (message.totalTrue !== undefined) {
      writer.uint32(24).int64(message.totalTrue);
    }
    if (message.totalFalse !== undefined) {
      writer.uint32(32).int64(message.totalFalse);
    }
    if (message.percentageTrue !== undefined) {
      writer.uint32(41).double(message.percentageTrue);
    }
    if (message.percentageFalse !== undefined) {
      writer.uint32(49).double(message.percentageFalse);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Boolean();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.count = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.type = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.totalTrue = longToNumber(reader.int64());
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.totalFalse = longToNumber(reader.int64());
          continue;
        case 5:
          if (tag !== 41) {
            break;
          }
          message.percentageTrue = reader.double();
          continue;
        case 6:
          if (tag !== 49) {
            break;
          }
          message.percentageFalse = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      count: isSet(object.count) ? globalThis.Number(object.count) : undefined,
      type: isSet(object.type) ? globalThis.String(object.type) : undefined,
      totalTrue: isSet(object.totalTrue) ? globalThis.Number(object.totalTrue) : undefined,
      totalFalse: isSet(object.totalFalse) ? globalThis.Number(object.totalFalse) : undefined,
      percentageTrue: isSet(object.percentageTrue) ? globalThis.Number(object.percentageTrue) : undefined,
      percentageFalse: isSet(object.percentageFalse) ? globalThis.Number(object.percentageFalse) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.count !== undefined) {
      obj.count = Math.round(message.count);
    }
    if (message.type !== undefined) {
      obj.type = message.type;
    }
    if (message.totalTrue !== undefined) {
      obj.totalTrue = Math.round(message.totalTrue);
    }
    if (message.totalFalse !== undefined) {
      obj.totalFalse = Math.round(message.totalFalse);
    }
    if (message.percentageTrue !== undefined) {
      obj.percentageTrue = message.percentageTrue;
    }
    if (message.percentageFalse !== undefined) {
      obj.percentageFalse = message.percentageFalse;
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Aggregations_Aggregation_Boolean.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Boolean();
    message.count = (_a = object.count) !== null && _a !== void 0 ? _a : undefined;
    message.type = (_b = object.type) !== null && _b !== void 0 ? _b : undefined;
    message.totalTrue = (_c = object.totalTrue) !== null && _c !== void 0 ? _c : undefined;
    message.totalFalse = (_d = object.totalFalse) !== null && _d !== void 0 ? _d : undefined;
    message.percentageTrue = (_e = object.percentageTrue) !== null && _e !== void 0 ? _e : undefined;
    message.percentageFalse = (_f = object.percentageFalse) !== null && _f !== void 0 ? _f : undefined;
    return message;
  },
};
function createBaseAggregateReply_Aggregations_Aggregation_DateMessage() {
  return {
    count: undefined,
    type: undefined,
    median: undefined,
    mode: undefined,
    maximum: undefined,
    minimum: undefined,
  };
}
exports.AggregateReply_Aggregations_Aggregation_DateMessage = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.count !== undefined) {
      writer.uint32(8).int64(message.count);
    }
    if (message.type !== undefined) {
      writer.uint32(18).string(message.type);
    }
    if (message.median !== undefined) {
      writer.uint32(26).string(message.median);
    }
    if (message.mode !== undefined) {
      writer.uint32(34).string(message.mode);
    }
    if (message.maximum !== undefined) {
      writer.uint32(42).string(message.maximum);
    }
    if (message.minimum !== undefined) {
      writer.uint32(50).string(message.minimum);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Aggregations_Aggregation_DateMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.count = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.type = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.median = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.mode = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.maximum = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.minimum = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      count: isSet(object.count) ? globalThis.Number(object.count) : undefined,
      type: isSet(object.type) ? globalThis.String(object.type) : undefined,
      median: isSet(object.median) ? globalThis.String(object.median) : undefined,
      mode: isSet(object.mode) ? globalThis.String(object.mode) : undefined,
      maximum: isSet(object.maximum) ? globalThis.String(object.maximum) : undefined,
      minimum: isSet(object.minimum) ? globalThis.String(object.minimum) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.count !== undefined) {
      obj.count = Math.round(message.count);
    }
    if (message.type !== undefined) {
      obj.type = message.type;
    }
    if (message.median !== undefined) {
      obj.median = message.median;
    }
    if (message.mode !== undefined) {
      obj.mode = message.mode;
    }
    if (message.maximum !== undefined) {
      obj.maximum = message.maximum;
    }
    if (message.minimum !== undefined) {
      obj.minimum = message.minimum;
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Aggregations_Aggregation_DateMessage.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f;
    const message = createBaseAggregateReply_Aggregations_Aggregation_DateMessage();
    message.count = (_a = object.count) !== null && _a !== void 0 ? _a : undefined;
    message.type = (_b = object.type) !== null && _b !== void 0 ? _b : undefined;
    message.median = (_c = object.median) !== null && _c !== void 0 ? _c : undefined;
    message.mode = (_d = object.mode) !== null && _d !== void 0 ? _d : undefined;
    message.maximum = (_e = object.maximum) !== null && _e !== void 0 ? _e : undefined;
    message.minimum = (_f = object.minimum) !== null && _f !== void 0 ? _f : undefined;
    return message;
  },
};
function createBaseAggregateReply_Aggregations_Aggregation_Reference() {
  return { type: undefined, pointingTo: [] };
}
exports.AggregateReply_Aggregations_Aggregation_Reference = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.type !== undefined) {
      writer.uint32(10).string(message.type);
    }
    for (const v of message.pointingTo) {
      writer.uint32(18).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Reference();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.type = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.pointingTo.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      type: isSet(object.type) ? globalThis.String(object.type) : undefined,
      pointingTo: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.pointingTo)
        ? object.pointingTo.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.type !== undefined) {
      obj.type = message.type;
    }
    if ((_a = message.pointingTo) === null || _a === void 0 ? void 0 : _a.length) {
      obj.pointingTo = message.pointingTo;
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Aggregations_Aggregation_Reference.fromPartial(
      base !== null && base !== void 0 ? base : {}
    );
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseAggregateReply_Aggregations_Aggregation_Reference();
    message.type = (_a = object.type) !== null && _a !== void 0 ? _a : undefined;
    message.pointingTo =
      ((_b = object.pointingTo) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
    return message;
  },
};
function createBaseAggregateReply_Single() {
  return { objectsCount: undefined, aggregations: undefined };
}
exports.AggregateReply_Single = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.objectsCount !== undefined) {
      writer.uint32(8).int64(message.objectsCount);
    }
    if (message.aggregations !== undefined) {
      exports.AggregateReply_Aggregations.encode(message.aggregations, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Single();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.objectsCount = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.aggregations = exports.AggregateReply_Aggregations.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      objectsCount: isSet(object.objectsCount) ? globalThis.Number(object.objectsCount) : undefined,
      aggregations: isSet(object.aggregations)
        ? exports.AggregateReply_Aggregations.fromJSON(object.aggregations)
        : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.objectsCount !== undefined) {
      obj.objectsCount = Math.round(message.objectsCount);
    }
    if (message.aggregations !== undefined) {
      obj.aggregations = exports.AggregateReply_Aggregations.toJSON(message.aggregations);
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Single.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseAggregateReply_Single();
    message.objectsCount = (_a = object.objectsCount) !== null && _a !== void 0 ? _a : undefined;
    message.aggregations =
      object.aggregations !== undefined && object.aggregations !== null
        ? exports.AggregateReply_Aggregations.fromPartial(object.aggregations)
        : undefined;
    return message;
  },
};
function createBaseAggregateReply_Group() {
  return { objectsCount: undefined, aggregations: undefined, groupedBy: undefined };
}
exports.AggregateReply_Group = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.objectsCount !== undefined) {
      writer.uint32(8).int64(message.objectsCount);
    }
    if (message.aggregations !== undefined) {
      exports.AggregateReply_Aggregations.encode(message.aggregations, writer.uint32(18).fork()).ldelim();
    }
    if (message.groupedBy !== undefined) {
      exports.AggregateReply_Group_GroupedBy.encode(message.groupedBy, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Group();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.objectsCount = longToNumber(reader.int64());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.aggregations = exports.AggregateReply_Aggregations.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.groupedBy = exports.AggregateReply_Group_GroupedBy.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      objectsCount: isSet(object.objectsCount) ? globalThis.Number(object.objectsCount) : undefined,
      aggregations: isSet(object.aggregations)
        ? exports.AggregateReply_Aggregations.fromJSON(object.aggregations)
        : undefined,
      groupedBy: isSet(object.groupedBy)
        ? exports.AggregateReply_Group_GroupedBy.fromJSON(object.groupedBy)
        : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.objectsCount !== undefined) {
      obj.objectsCount = Math.round(message.objectsCount);
    }
    if (message.aggregations !== undefined) {
      obj.aggregations = exports.AggregateReply_Aggregations.toJSON(message.aggregations);
    }
    if (message.groupedBy !== undefined) {
      obj.groupedBy = exports.AggregateReply_Group_GroupedBy.toJSON(message.groupedBy);
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Group.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseAggregateReply_Group();
    message.objectsCount = (_a = object.objectsCount) !== null && _a !== void 0 ? _a : undefined;
    message.aggregations =
      object.aggregations !== undefined && object.aggregations !== null
        ? exports.AggregateReply_Aggregations.fromPartial(object.aggregations)
        : undefined;
    message.groupedBy =
      object.groupedBy !== undefined && object.groupedBy !== null
        ? exports.AggregateReply_Group_GroupedBy.fromPartial(object.groupedBy)
        : undefined;
    return message;
  },
};
function createBaseAggregateReply_Group_GroupedBy() {
  return {
    path: [],
    text: undefined,
    int: undefined,
    boolean: undefined,
    number: undefined,
    texts: undefined,
    ints: undefined,
    booleans: undefined,
    numbers: undefined,
    geo: undefined,
  };
}
exports.AggregateReply_Group_GroupedBy = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.path) {
      writer.uint32(10).string(v);
    }
    if (message.text !== undefined) {
      writer.uint32(18).string(message.text);
    }
    if (message.int !== undefined) {
      writer.uint32(24).int64(message.int);
    }
    if (message.boolean !== undefined) {
      writer.uint32(32).bool(message.boolean);
    }
    if (message.number !== undefined) {
      writer.uint32(41).double(message.number);
    }
    if (message.texts !== undefined) {
      base_js_1.TextArray.encode(message.texts, writer.uint32(50).fork()).ldelim();
    }
    if (message.ints !== undefined) {
      base_js_1.IntArray.encode(message.ints, writer.uint32(58).fork()).ldelim();
    }
    if (message.booleans !== undefined) {
      base_js_1.BooleanArray.encode(message.booleans, writer.uint32(66).fork()).ldelim();
    }
    if (message.numbers !== undefined) {
      base_js_1.NumberArray.encode(message.numbers, writer.uint32(74).fork()).ldelim();
    }
    if (message.geo !== undefined) {
      base_js_1.GeoCoordinatesFilter.encode(message.geo, writer.uint32(82).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Group_GroupedBy();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.path.push(reader.string());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.text = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.int = longToNumber(reader.int64());
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.boolean = reader.bool();
          continue;
        case 5:
          if (tag !== 41) {
            break;
          }
          message.number = reader.double();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.texts = base_js_1.TextArray.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.ints = base_js_1.IntArray.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }
          message.booleans = base_js_1.BooleanArray.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.numbers = base_js_1.NumberArray.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.geo = base_js_1.GeoCoordinatesFilter.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      path: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.path)
        ? object.path.map((e) => globalThis.String(e))
        : [],
      text: isSet(object.text) ? globalThis.String(object.text) : undefined,
      int: isSet(object.int) ? globalThis.Number(object.int) : undefined,
      boolean: isSet(object.boolean) ? globalThis.Boolean(object.boolean) : undefined,
      number: isSet(object.number) ? globalThis.Number(object.number) : undefined,
      texts: isSet(object.texts) ? base_js_1.TextArray.fromJSON(object.texts) : undefined,
      ints: isSet(object.ints) ? base_js_1.IntArray.fromJSON(object.ints) : undefined,
      booleans: isSet(object.booleans) ? base_js_1.BooleanArray.fromJSON(object.booleans) : undefined,
      numbers: isSet(object.numbers) ? base_js_1.NumberArray.fromJSON(object.numbers) : undefined,
      geo: isSet(object.geo) ? base_js_1.GeoCoordinatesFilter.fromJSON(object.geo) : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.path) === null || _a === void 0 ? void 0 : _a.length) {
      obj.path = message.path;
    }
    if (message.text !== undefined) {
      obj.text = message.text;
    }
    if (message.int !== undefined) {
      obj.int = Math.round(message.int);
    }
    if (message.boolean !== undefined) {
      obj.boolean = message.boolean;
    }
    if (message.number !== undefined) {
      obj.number = message.number;
    }
    if (message.texts !== undefined) {
      obj.texts = base_js_1.TextArray.toJSON(message.texts);
    }
    if (message.ints !== undefined) {
      obj.ints = base_js_1.IntArray.toJSON(message.ints);
    }
    if (message.booleans !== undefined) {
      obj.booleans = base_js_1.BooleanArray.toJSON(message.booleans);
    }
    if (message.numbers !== undefined) {
      obj.numbers = base_js_1.NumberArray.toJSON(message.numbers);
    }
    if (message.geo !== undefined) {
      obj.geo = base_js_1.GeoCoordinatesFilter.toJSON(message.geo);
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Group_GroupedBy.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e;
    const message = createBaseAggregateReply_Group_GroupedBy();
    message.path = ((_a = object.path) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    message.text = (_b = object.text) !== null && _b !== void 0 ? _b : undefined;
    message.int = (_c = object.int) !== null && _c !== void 0 ? _c : undefined;
    message.boolean = (_d = object.boolean) !== null && _d !== void 0 ? _d : undefined;
    message.number = (_e = object.number) !== null && _e !== void 0 ? _e : undefined;
    message.texts =
      object.texts !== undefined && object.texts !== null
        ? base_js_1.TextArray.fromPartial(object.texts)
        : undefined;
    message.ints =
      object.ints !== undefined && object.ints !== null
        ? base_js_1.IntArray.fromPartial(object.ints)
        : undefined;
    message.booleans =
      object.booleans !== undefined && object.booleans !== null
        ? base_js_1.BooleanArray.fromPartial(object.booleans)
        : undefined;
    message.numbers =
      object.numbers !== undefined && object.numbers !== null
        ? base_js_1.NumberArray.fromPartial(object.numbers)
        : undefined;
    message.geo =
      object.geo !== undefined && object.geo !== null
        ? base_js_1.GeoCoordinatesFilter.fromPartial(object.geo)
        : undefined;
    return message;
  },
};
function createBaseAggregateReply_Grouped() {
  return { groups: [] };
}
exports.AggregateReply_Grouped = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.groups) {
      exports.AggregateReply_Group.encode(v, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAggregateReply_Grouped();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.groups.push(exports.AggregateReply_Group.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      groups: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.groups)
        ? object.groups.map((e) => exports.AggregateReply_Group.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.groups) === null || _a === void 0 ? void 0 : _a.length) {
      obj.groups = message.groups.map((e) => exports.AggregateReply_Group.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return exports.AggregateReply_Grouped.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseAggregateReply_Grouped();
    message.groups =
      ((_a = object.groups) === null || _a === void 0
        ? void 0
        : _a.map((e) => exports.AggregateReply_Group.fromPartial(e))) || [];
    return message;
  },
};
function longToNumber(long) {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error('Value is larger than Number.MAX_SAFE_INTEGER');
  }
  return long.toNumber();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
  minimal_js_1.default.util.Long = long_1.default;
  minimal_js_1.default.configure();
}
function isSet(value) {
  return value !== null && value !== undefined;
}
