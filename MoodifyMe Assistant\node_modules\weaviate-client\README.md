# Weaviate JS/TS client <img alt='Weaviate logo' src='https://weaviate.io/img/site/weaviate-logo-light.png' width='148' align='right' />

Official JS/TS client for easy interaction with a Weaviate instance.

## Documentation

- [General Documentation](https://weaviate.io/developers/weaviate/client-libraries/typescript).
- [Client-specific Documentation](https://weaviate.github.io/typescript-client/)

## Support

- [Stackoverflow for questions](https://stackoverflow.com/questions/tagged/weaviate).
- [Github for issues](https://github.com/weaviate/typescript-client/issues).

## Contributing

- [How to Contribute](https://github.com/weaviate/typescript-client/blob/main/CONTRIBUTE.md).

## Build Status

[![Build Status](https://github.com/weaviate/typescript-client/actions/workflows/.github/workflows/main.yaml/badge.svg?branch=main)](https://github.com/weaviate/typescript-client/actions/workflows/.github/workflows/main.yaml)
