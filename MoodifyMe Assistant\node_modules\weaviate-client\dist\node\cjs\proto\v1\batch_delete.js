'use strict';
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.176.0
//   protoc               v3.19.1
// source: v1/batch_delete.proto
var __importDefault =
  (this && this.__importDefault) ||
  function (mod) {
    return mod && mod.__esModule ? mod : { default: mod };
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.BatchDeleteObject =
  exports.BatchDeleteReply =
  exports.BatchDeleteRequest =
  exports.protobufPackage =
    void 0;
/* eslint-disable */
const long_1 = __importDefault(require('long'));
const minimal_js_1 = __importDefault(require('protobufjs/minimal.js'));
const base_js_1 = require('./base.js');
exports.protobufPackage = 'weaviate.v1';
function createBaseBatchDeleteRequest() {
  return {
    collection: '',
    filters: undefined,
    verbose: false,
    dryRun: false,
    consistencyLevel: undefined,
    tenant: undefined,
  };
}
exports.BatchDeleteRequest = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.collection !== '') {
      writer.uint32(10).string(message.collection);
    }
    if (message.filters !== undefined) {
      base_js_1.Filters.encode(message.filters, writer.uint32(18).fork()).ldelim();
    }
    if (message.verbose !== false) {
      writer.uint32(24).bool(message.verbose);
    }
    if (message.dryRun !== false) {
      writer.uint32(32).bool(message.dryRun);
    }
    if (message.consistencyLevel !== undefined) {
      writer.uint32(40).int32(message.consistencyLevel);
    }
    if (message.tenant !== undefined) {
      writer.uint32(50).string(message.tenant);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBatchDeleteRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.collection = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.filters = base_js_1.Filters.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.verbose = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.dryRun = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.consistencyLevel = reader.int32();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.tenant = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      collection: isSet(object.collection) ? globalThis.String(object.collection) : '',
      filters: isSet(object.filters) ? base_js_1.Filters.fromJSON(object.filters) : undefined,
      verbose: isSet(object.verbose) ? globalThis.Boolean(object.verbose) : false,
      dryRun: isSet(object.dryRun) ? globalThis.Boolean(object.dryRun) : false,
      consistencyLevel: isSet(object.consistencyLevel)
        ? (0, base_js_1.consistencyLevelFromJSON)(object.consistencyLevel)
        : undefined,
      tenant: isSet(object.tenant) ? globalThis.String(object.tenant) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.collection !== '') {
      obj.collection = message.collection;
    }
    if (message.filters !== undefined) {
      obj.filters = base_js_1.Filters.toJSON(message.filters);
    }
    if (message.verbose !== false) {
      obj.verbose = message.verbose;
    }
    if (message.dryRun !== false) {
      obj.dryRun = message.dryRun;
    }
    if (message.consistencyLevel !== undefined) {
      obj.consistencyLevel = (0, base_js_1.consistencyLevelToJSON)(message.consistencyLevel);
    }
    if (message.tenant !== undefined) {
      obj.tenant = message.tenant;
    }
    return obj;
  },
  create(base) {
    return exports.BatchDeleteRequest.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e;
    const message = createBaseBatchDeleteRequest();
    message.collection = (_a = object.collection) !== null && _a !== void 0 ? _a : '';
    message.filters =
      object.filters !== undefined && object.filters !== null
        ? base_js_1.Filters.fromPartial(object.filters)
        : undefined;
    message.verbose = (_b = object.verbose) !== null && _b !== void 0 ? _b : false;
    message.dryRun = (_c = object.dryRun) !== null && _c !== void 0 ? _c : false;
    message.consistencyLevel = (_d = object.consistencyLevel) !== null && _d !== void 0 ? _d : undefined;
    message.tenant = (_e = object.tenant) !== null && _e !== void 0 ? _e : undefined;
    return message;
  },
};
function createBaseBatchDeleteReply() {
  return { took: 0, failed: 0, matches: 0, successful: 0, objects: [] };
}
exports.BatchDeleteReply = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.took !== 0) {
      writer.uint32(13).float(message.took);
    }
    if (message.failed !== 0) {
      writer.uint32(16).int64(message.failed);
    }
    if (message.matches !== 0) {
      writer.uint32(24).int64(message.matches);
    }
    if (message.successful !== 0) {
      writer.uint32(32).int64(message.successful);
    }
    for (const v of message.objects) {
      exports.BatchDeleteObject.encode(v, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBatchDeleteReply();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 13) {
            break;
          }
          message.took = reader.float();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.failed = longToNumber(reader.int64());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.matches = longToNumber(reader.int64());
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.successful = longToNumber(reader.int64());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.objects.push(exports.BatchDeleteObject.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      took: isSet(object.took) ? globalThis.Number(object.took) : 0,
      failed: isSet(object.failed) ? globalThis.Number(object.failed) : 0,
      matches: isSet(object.matches) ? globalThis.Number(object.matches) : 0,
      successful: isSet(object.successful) ? globalThis.Number(object.successful) : 0,
      objects: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.objects)
        ? object.objects.map((e) => exports.BatchDeleteObject.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.took !== 0) {
      obj.took = message.took;
    }
    if (message.failed !== 0) {
      obj.failed = Math.round(message.failed);
    }
    if (message.matches !== 0) {
      obj.matches = Math.round(message.matches);
    }
    if (message.successful !== 0) {
      obj.successful = Math.round(message.successful);
    }
    if ((_a = message.objects) === null || _a === void 0 ? void 0 : _a.length) {
      obj.objects = message.objects.map((e) => exports.BatchDeleteObject.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return exports.BatchDeleteReply.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e;
    const message = createBaseBatchDeleteReply();
    message.took = (_a = object.took) !== null && _a !== void 0 ? _a : 0;
    message.failed = (_b = object.failed) !== null && _b !== void 0 ? _b : 0;
    message.matches = (_c = object.matches) !== null && _c !== void 0 ? _c : 0;
    message.successful = (_d = object.successful) !== null && _d !== void 0 ? _d : 0;
    message.objects =
      ((_e = object.objects) === null || _e === void 0
        ? void 0
        : _e.map((e) => exports.BatchDeleteObject.fromPartial(e))) || [];
    return message;
  },
};
function createBaseBatchDeleteObject() {
  return { uuid: new Uint8Array(0), successful: false, error: undefined };
}
exports.BatchDeleteObject = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.uuid.length !== 0) {
      writer.uint32(10).bytes(message.uuid);
    }
    if (message.successful !== false) {
      writer.uint32(16).bool(message.successful);
    }
    if (message.error !== undefined) {
      writer.uint32(26).string(message.error);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBatchDeleteObject();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.uuid = reader.bytes();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.successful = reader.bool();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.error = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      uuid: isSet(object.uuid) ? bytesFromBase64(object.uuid) : new Uint8Array(0),
      successful: isSet(object.successful) ? globalThis.Boolean(object.successful) : false,
      error: isSet(object.error) ? globalThis.String(object.error) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.uuid.length !== 0) {
      obj.uuid = base64FromBytes(message.uuid);
    }
    if (message.successful !== false) {
      obj.successful = message.successful;
    }
    if (message.error !== undefined) {
      obj.error = message.error;
    }
    return obj;
  },
  create(base) {
    return exports.BatchDeleteObject.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseBatchDeleteObject();
    message.uuid = (_a = object.uuid) !== null && _a !== void 0 ? _a : new Uint8Array(0);
    message.successful = (_b = object.successful) !== null && _b !== void 0 ? _b : false;
    message.error = (_c = object.error) !== null && _c !== void 0 ? _c : undefined;
    return message;
  },
};
function bytesFromBase64(b64) {
  if (globalThis.Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}
function base64FromBytes(arr) {
  if (globalThis.Buffer) {
    return globalThis.Buffer.from(arr).toString('base64');
  } else {
    const bin = [];
    arr.forEach((byte) => {
      bin.push(globalThis.String.fromCharCode(byte));
    });
    return globalThis.btoa(bin.join(''));
  }
}
function longToNumber(long) {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error('Value is larger than Number.MAX_SAFE_INTEGER');
  }
  return long.toNumber();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
  minimal_js_1.default.util.Long = long_1.default;
  minimal_js_1.default.configure();
}
function isSet(value) {
  return value !== null && value !== undefined;
}
