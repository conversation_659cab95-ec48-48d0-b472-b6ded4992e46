import Connection from '../connection/index.js';
import { DbVersionSupport } from '../utils/dbVersion.js';
import Checker from './checker.js';
import Creator from './creator.js';
import Deleter from './deleter.js';
import Getter from './getter.js';
import GetterById from './getterById.js';
import Merger from './merger.js';
import ReferenceCreator from './referenceCreator.js';
import ReferenceDeleter from './referenceDeleter.js';
import ReferencePayloadBuilder from './referencePayloadBuilder.js';
import ReferenceReplacer from './referenceReplacer.js';
import Updater from './updater.js';
import Validator from './validator.js';
export interface Data {
  creator: () => Creator;
  validator: () => Validator;
  updater: () => Updater;
  merger: () => Merger;
  getter: () => Getter;
  getterById: () => GetterById;
  deleter: () => Deleter;
  checker: () => Checker;
  referenceCreator: () => ReferenceCreator;
  referenceReplacer: () => ReferenceReplacer;
  referenceDeleter: () => ReferenceDeleter;
  referencePayloadBuilder: () => ReferencePayloadBuilder;
}
declare const data: (client: Connection, dbVersionSupport: DbVersionSupport) => Data;
export default data;
export { default as Checker } from './checker.js';
export { default as Creator } from './creator.js';
export { default as Deleter } from './deleter.js';
export { default as Getter } from './getter.js';
export { default as GetterById } from './getterById.js';
export { default as Merger } from './merger.js';
export { default as ReferenceCreator } from './referenceCreator.js';
export { default as ReferenceDeleter } from './referenceDeleter.js';
export { default as ReferencePayloadBuilder } from './referencePayloadBuilder.js';
export { default as ReferenceReplacer } from './referenceReplacer.js';
export type { ConsistencyLevel } from './replication.js';
export { default as Updater } from './updater.js';
export { default as Validator } from './validator.js';
