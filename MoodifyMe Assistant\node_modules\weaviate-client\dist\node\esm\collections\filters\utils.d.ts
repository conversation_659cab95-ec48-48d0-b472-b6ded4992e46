import { CountRef, FilterTargetInternal, MultiTargetRef, SingleTargetRef } from './types.js';
export declare class TargetGuards {
  static isSingleTargetRef(target?: FilterTargetInternal): target is SingleTargetRef;
  static isMultiTargetRef(target?: FilterTargetInternal): target is MultiTargetRef;
  static isCountRef(target?: FilterTargetInternal): target is CountRef;
  static isProperty(target?: FilterTargetInternal): target is string;
  static isTargetRef(target?: FilterTargetInternal): target is SingleTargetRef | MultiTargetRef;
}
