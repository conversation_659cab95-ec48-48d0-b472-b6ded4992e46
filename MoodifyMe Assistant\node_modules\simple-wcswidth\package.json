{"name": "simple-wcswidth", "version": "1.0.1", "description": "Simplified JS/TS implementation of wcswidth() written by <PERSON> in C", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": "**************:ayonious/simple-wcswidth.git", "author": "<PERSON><PERSON><PERSON> <no<PERSON><PERSON><PERSON>@gmail.com>", "license": "MIT", "scripts": {"setup": "yarn", "build": "tsc", "format": "prettier --write \"**/*.{json,md,ts,tsx,yml,js,jsx}\"", "test": "jest --config jestconfig.json", "lint": "eslint --ext=js,ts ."}, "keywords": ["wcwidth", "wcswidth", "console-width"], "files": ["dist"], "devDependencies": {"@semantic-release/changelog": "^5.0.1", "@semantic-release/git": "^9.0.0", "@types/jest": "^26.0.16", "@typescript-eslint/eslint-plugin": "^4.9.0", "@typescript-eslint/parser": "^4.9.0", "eslint": "^7.15.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^6.15.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.2.0", "husky": "^4.3.0", "jest": "^26.6.3", "prettier": "^2.2.1", "pretty-quick": "^3.1.0", "semantic-release": "^17.3.0", "ts-jest": "^26.4.4", "typescript": "^4.1.2"}}