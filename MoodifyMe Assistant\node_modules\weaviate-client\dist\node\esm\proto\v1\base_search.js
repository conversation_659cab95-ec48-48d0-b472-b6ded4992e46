// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.176.0
//   protoc               v3.19.1
// source: v1/base_search.proto
/* eslint-disable */
import _m0 from 'protobufjs/minimal.js';
import { Vectors } from './base.js';
export const protobufPackage = 'weaviate.v1';
export var CombinationMethod;
(function (CombinationMethod) {
  CombinationMethod[(CombinationMethod['COMBINATION_METHOD_UNSPECIFIED'] = 0)] =
    'COMBINATION_METHOD_UNSPECIFIED';
  CombinationMethod[(CombinationMethod['COMBINATION_METHOD_TYPE_SUM'] = 1)] = 'COMBINATION_METHOD_TYPE_SUM';
  CombinationMethod[(CombinationMethod['COMBINATION_METHOD_TYPE_MIN'] = 2)] = 'COMBINATION_METHOD_TYPE_MIN';
  CombinationMethod[(CombinationMethod['COMBINATION_METHOD_TYPE_AVERAGE'] = 3)] =
    'COMBINATION_METHOD_TYPE_AVERAGE';
  CombinationMethod[(CombinationMethod['COMBINATION_METHOD_TYPE_RELATIVE_SCORE'] = 4)] =
    'COMBINATION_METHOD_TYPE_RELATIVE_SCORE';
  CombinationMethod[(CombinationMethod['COMBINATION_METHOD_TYPE_MANUAL'] = 5)] =
    'COMBINATION_METHOD_TYPE_MANUAL';
  CombinationMethod[(CombinationMethod['UNRECOGNIZED'] = -1)] = 'UNRECOGNIZED';
})(CombinationMethod || (CombinationMethod = {}));
export function combinationMethodFromJSON(object) {
  switch (object) {
    case 0:
    case 'COMBINATION_METHOD_UNSPECIFIED':
      return CombinationMethod.COMBINATION_METHOD_UNSPECIFIED;
    case 1:
    case 'COMBINATION_METHOD_TYPE_SUM':
      return CombinationMethod.COMBINATION_METHOD_TYPE_SUM;
    case 2:
    case 'COMBINATION_METHOD_TYPE_MIN':
      return CombinationMethod.COMBINATION_METHOD_TYPE_MIN;
    case 3:
    case 'COMBINATION_METHOD_TYPE_AVERAGE':
      return CombinationMethod.COMBINATION_METHOD_TYPE_AVERAGE;
    case 4:
    case 'COMBINATION_METHOD_TYPE_RELATIVE_SCORE':
      return CombinationMethod.COMBINATION_METHOD_TYPE_RELATIVE_SCORE;
    case 5:
    case 'COMBINATION_METHOD_TYPE_MANUAL':
      return CombinationMethod.COMBINATION_METHOD_TYPE_MANUAL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CombinationMethod.UNRECOGNIZED;
  }
}
export function combinationMethodToJSON(object) {
  switch (object) {
    case CombinationMethod.COMBINATION_METHOD_UNSPECIFIED:
      return 'COMBINATION_METHOD_UNSPECIFIED';
    case CombinationMethod.COMBINATION_METHOD_TYPE_SUM:
      return 'COMBINATION_METHOD_TYPE_SUM';
    case CombinationMethod.COMBINATION_METHOD_TYPE_MIN:
      return 'COMBINATION_METHOD_TYPE_MIN';
    case CombinationMethod.COMBINATION_METHOD_TYPE_AVERAGE:
      return 'COMBINATION_METHOD_TYPE_AVERAGE';
    case CombinationMethod.COMBINATION_METHOD_TYPE_RELATIVE_SCORE:
      return 'COMBINATION_METHOD_TYPE_RELATIVE_SCORE';
    case CombinationMethod.COMBINATION_METHOD_TYPE_MANUAL:
      return 'COMBINATION_METHOD_TYPE_MANUAL';
    case CombinationMethod.UNRECOGNIZED:
    default:
      return 'UNRECOGNIZED';
  }
}
export var SearchOperatorOptions_Operator;
(function (SearchOperatorOptions_Operator) {
  SearchOperatorOptions_Operator[(SearchOperatorOptions_Operator['OPERATOR_UNSPECIFIED'] = 0)] =
    'OPERATOR_UNSPECIFIED';
  SearchOperatorOptions_Operator[(SearchOperatorOptions_Operator['OPERATOR_OR'] = 1)] = 'OPERATOR_OR';
  SearchOperatorOptions_Operator[(SearchOperatorOptions_Operator['OPERATOR_AND'] = 2)] = 'OPERATOR_AND';
  SearchOperatorOptions_Operator[(SearchOperatorOptions_Operator['UNRECOGNIZED'] = -1)] = 'UNRECOGNIZED';
})(SearchOperatorOptions_Operator || (SearchOperatorOptions_Operator = {}));
export function searchOperatorOptions_OperatorFromJSON(object) {
  switch (object) {
    case 0:
    case 'OPERATOR_UNSPECIFIED':
      return SearchOperatorOptions_Operator.OPERATOR_UNSPECIFIED;
    case 1:
    case 'OPERATOR_OR':
      return SearchOperatorOptions_Operator.OPERATOR_OR;
    case 2:
    case 'OPERATOR_AND':
      return SearchOperatorOptions_Operator.OPERATOR_AND;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SearchOperatorOptions_Operator.UNRECOGNIZED;
  }
}
export function searchOperatorOptions_OperatorToJSON(object) {
  switch (object) {
    case SearchOperatorOptions_Operator.OPERATOR_UNSPECIFIED:
      return 'OPERATOR_UNSPECIFIED';
    case SearchOperatorOptions_Operator.OPERATOR_OR:
      return 'OPERATOR_OR';
    case SearchOperatorOptions_Operator.OPERATOR_AND:
      return 'OPERATOR_AND';
    case SearchOperatorOptions_Operator.UNRECOGNIZED:
    default:
      return 'UNRECOGNIZED';
  }
}
export var Hybrid_FusionType;
(function (Hybrid_FusionType) {
  Hybrid_FusionType[(Hybrid_FusionType['FUSION_TYPE_UNSPECIFIED'] = 0)] = 'FUSION_TYPE_UNSPECIFIED';
  Hybrid_FusionType[(Hybrid_FusionType['FUSION_TYPE_RANKED'] = 1)] = 'FUSION_TYPE_RANKED';
  Hybrid_FusionType[(Hybrid_FusionType['FUSION_TYPE_RELATIVE_SCORE'] = 2)] = 'FUSION_TYPE_RELATIVE_SCORE';
  Hybrid_FusionType[(Hybrid_FusionType['UNRECOGNIZED'] = -1)] = 'UNRECOGNIZED';
})(Hybrid_FusionType || (Hybrid_FusionType = {}));
export function hybrid_FusionTypeFromJSON(object) {
  switch (object) {
    case 0:
    case 'FUSION_TYPE_UNSPECIFIED':
      return Hybrid_FusionType.FUSION_TYPE_UNSPECIFIED;
    case 1:
    case 'FUSION_TYPE_RANKED':
      return Hybrid_FusionType.FUSION_TYPE_RANKED;
    case 2:
    case 'FUSION_TYPE_RELATIVE_SCORE':
      return Hybrid_FusionType.FUSION_TYPE_RELATIVE_SCORE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Hybrid_FusionType.UNRECOGNIZED;
  }
}
export function hybrid_FusionTypeToJSON(object) {
  switch (object) {
    case Hybrid_FusionType.FUSION_TYPE_UNSPECIFIED:
      return 'FUSION_TYPE_UNSPECIFIED';
    case Hybrid_FusionType.FUSION_TYPE_RANKED:
      return 'FUSION_TYPE_RANKED';
    case Hybrid_FusionType.FUSION_TYPE_RELATIVE_SCORE:
      return 'FUSION_TYPE_RELATIVE_SCORE';
    case Hybrid_FusionType.UNRECOGNIZED:
    default:
      return 'UNRECOGNIZED';
  }
}
function createBaseWeightsForTarget() {
  return { target: '', weight: 0 };
}
export const WeightsForTarget = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.target !== '') {
      writer.uint32(10).string(message.target);
    }
    if (message.weight !== 0) {
      writer.uint32(21).float(message.weight);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWeightsForTarget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.target = reader.string();
          continue;
        case 2:
          if (tag !== 21) {
            break;
          }
          message.weight = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      target: isSet(object.target) ? globalThis.String(object.target) : '',
      weight: isSet(object.weight) ? globalThis.Number(object.weight) : 0,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.target !== '') {
      obj.target = message.target;
    }
    if (message.weight !== 0) {
      obj.weight = message.weight;
    }
    return obj;
  },
  create(base) {
    return WeightsForTarget.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseWeightsForTarget();
    message.target = (_a = object.target) !== null && _a !== void 0 ? _a : '';
    message.weight = (_b = object.weight) !== null && _b !== void 0 ? _b : 0;
    return message;
  },
};
function createBaseTargets() {
  return { targetVectors: [], combination: 0, weights: {}, weightsForTargets: [] };
}
export const Targets = {
  encode(message, writer = _m0.Writer.create()) {
    for (const v of message.targetVectors) {
      writer.uint32(10).string(v);
    }
    if (message.combination !== 0) {
      writer.uint32(16).int32(message.combination);
    }
    Object.entries(message.weights).forEach(([key, value]) => {
      Targets_WeightsEntry.encode({ key: key, value }, writer.uint32(26).fork()).ldelim();
    });
    for (const v of message.weightsForTargets) {
      WeightsForTarget.encode(v, writer.uint32(34).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTargets();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.targetVectors.push(reader.string());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.combination = reader.int32();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          const entry3 = Targets_WeightsEntry.decode(reader, reader.uint32());
          if (entry3.value !== undefined) {
            message.weights[entry3.key] = entry3.value;
          }
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.weightsForTargets.push(WeightsForTarget.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      targetVectors: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.targetVectors
      )
        ? object.targetVectors.map((e) => globalThis.String(e))
        : [],
      combination: isSet(object.combination) ? combinationMethodFromJSON(object.combination) : 0,
      weights: isObject(object.weights)
        ? Object.entries(object.weights).reduce((acc, [key, value]) => {
            acc[key] = Number(value);
            return acc;
          }, {})
        : {},
      weightsForTargets: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.weightsForTargets
      )
        ? object.weightsForTargets.map((e) => WeightsForTarget.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a, _b;
    const obj = {};
    if ((_a = message.targetVectors) === null || _a === void 0 ? void 0 : _a.length) {
      obj.targetVectors = message.targetVectors;
    }
    if (message.combination !== 0) {
      obj.combination = combinationMethodToJSON(message.combination);
    }
    if (message.weights) {
      const entries = Object.entries(message.weights);
      if (entries.length > 0) {
        obj.weights = {};
        entries.forEach(([k, v]) => {
          obj.weights[k] = v;
        });
      }
    }
    if ((_b = message.weightsForTargets) === null || _b === void 0 ? void 0 : _b.length) {
      obj.weightsForTargets = message.weightsForTargets.map((e) => WeightsForTarget.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return Targets.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseTargets();
    message.targetVectors =
      ((_a = object.targetVectors) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    message.combination = (_b = object.combination) !== null && _b !== void 0 ? _b : 0;
    message.weights = Object.entries((_c = object.weights) !== null && _c !== void 0 ? _c : {}).reduce(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.weightsForTargets =
      ((_d = object.weightsForTargets) === null || _d === void 0
        ? void 0
        : _d.map((e) => WeightsForTarget.fromPartial(e))) || [];
    return message;
  },
};
function createBaseTargets_WeightsEntry() {
  return { key: '', value: 0 };
}
export const Targets_WeightsEntry = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.key !== '') {
      writer.uint32(10).string(message.key);
    }
    if (message.value !== 0) {
      writer.uint32(21).float(message.value);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseTargets_WeightsEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 21) {
            break;
          }
          message.value = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.key !== '') {
      obj.key = message.key;
    }
    if (message.value !== 0) {
      obj.value = message.value;
    }
    return obj;
  },
  create(base) {
    return Targets_WeightsEntry.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseTargets_WeightsEntry();
    message.key = (_a = object.key) !== null && _a !== void 0 ? _a : '';
    message.value = (_b = object.value) !== null && _b !== void 0 ? _b : 0;
    return message;
  },
};
function createBaseVectorForTarget() {
  return { name: '', vectorBytes: new Uint8Array(0), vectors: [] };
}
export const VectorForTarget = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.name !== '') {
      writer.uint32(10).string(message.name);
    }
    if (message.vectorBytes.length !== 0) {
      writer.uint32(18).bytes(message.vectorBytes);
    }
    for (const v of message.vectors) {
      Vectors.encode(v, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseVectorForTarget();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.vectorBytes = reader.bytes();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.vectors.push(Vectors.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      vectorBytes: isSet(object.vectorBytes) ? bytesFromBase64(object.vectorBytes) : new Uint8Array(0),
      vectors: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.vectors)
        ? object.vectors.map((e) => Vectors.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.name !== '') {
      obj.name = message.name;
    }
    if (message.vectorBytes.length !== 0) {
      obj.vectorBytes = base64FromBytes(message.vectorBytes);
    }
    if ((_a = message.vectors) === null || _a === void 0 ? void 0 : _a.length) {
      obj.vectors = message.vectors.map((e) => Vectors.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return VectorForTarget.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseVectorForTarget();
    message.name = (_a = object.name) !== null && _a !== void 0 ? _a : '';
    message.vectorBytes = (_b = object.vectorBytes) !== null && _b !== void 0 ? _b : new Uint8Array(0);
    message.vectors =
      ((_c = object.vectors) === null || _c === void 0 ? void 0 : _c.map((e) => Vectors.fromPartial(e))) ||
      [];
    return message;
  },
};
function createBaseSearchOperatorOptions() {
  return { operator: 0, minimumOrTokensMatch: undefined };
}
export const SearchOperatorOptions = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.operator !== 0) {
      writer.uint32(8).int32(message.operator);
    }
    if (message.minimumOrTokensMatch !== undefined) {
      writer.uint32(16).int32(message.minimumOrTokensMatch);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchOperatorOptions();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.operator = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.minimumOrTokensMatch = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      operator: isSet(object.operator) ? searchOperatorOptions_OperatorFromJSON(object.operator) : 0,
      minimumOrTokensMatch: isSet(object.minimumOrTokensMatch)
        ? globalThis.Number(object.minimumOrTokensMatch)
        : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.operator !== 0) {
      obj.operator = searchOperatorOptions_OperatorToJSON(message.operator);
    }
    if (message.minimumOrTokensMatch !== undefined) {
      obj.minimumOrTokensMatch = Math.round(message.minimumOrTokensMatch);
    }
    return obj;
  },
  create(base) {
    return SearchOperatorOptions.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseSearchOperatorOptions();
    message.operator = (_a = object.operator) !== null && _a !== void 0 ? _a : 0;
    message.minimumOrTokensMatch =
      (_b = object.minimumOrTokensMatch) !== null && _b !== void 0 ? _b : undefined;
    return message;
  },
};
function createBaseHybrid() {
  return {
    query: '',
    properties: [],
    vector: [],
    alpha: 0,
    fusionType: 0,
    vectorBytes: new Uint8Array(0),
    targetVectors: [],
    nearText: undefined,
    nearVector: undefined,
    targets: undefined,
    bm25SearchOperator: undefined,
    vectorDistance: undefined,
    vectors: [],
  };
}
export const Hybrid = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.query !== '') {
      writer.uint32(10).string(message.query);
    }
    for (const v of message.properties) {
      writer.uint32(18).string(v);
    }
    writer.uint32(26).fork();
    for (const v of message.vector) {
      writer.float(v);
    }
    writer.ldelim();
    if (message.alpha !== 0) {
      writer.uint32(37).float(message.alpha);
    }
    if (message.fusionType !== 0) {
      writer.uint32(40).int32(message.fusionType);
    }
    if (message.vectorBytes.length !== 0) {
      writer.uint32(50).bytes(message.vectorBytes);
    }
    for (const v of message.targetVectors) {
      writer.uint32(58).string(v);
    }
    if (message.nearText !== undefined) {
      NearTextSearch.encode(message.nearText, writer.uint32(66).fork()).ldelim();
    }
    if (message.nearVector !== undefined) {
      NearVector.encode(message.nearVector, writer.uint32(74).fork()).ldelim();
    }
    if (message.targets !== undefined) {
      Targets.encode(message.targets, writer.uint32(82).fork()).ldelim();
    }
    if (message.bm25SearchOperator !== undefined) {
      SearchOperatorOptions.encode(message.bm25SearchOperator, writer.uint32(90).fork()).ldelim();
    }
    if (message.vectorDistance !== undefined) {
      writer.uint32(165).float(message.vectorDistance);
    }
    for (const v of message.vectors) {
      Vectors.encode(v, writer.uint32(170).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseHybrid();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.query = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.properties.push(reader.string());
          continue;
        case 3:
          if (tag === 29) {
            message.vector.push(reader.float());
            continue;
          }
          if (tag === 26) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.vector.push(reader.float());
            }
            continue;
          }
          break;
        case 4:
          if (tag !== 37) {
            break;
          }
          message.alpha = reader.float();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.fusionType = reader.int32();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.vectorBytes = reader.bytes();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.targetVectors.push(reader.string());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }
          message.nearText = NearTextSearch.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.nearVector = NearVector.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.targets = Targets.decode(reader, reader.uint32());
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }
          message.bm25SearchOperator = SearchOperatorOptions.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 165) {
            break;
          }
          message.vectorDistance = reader.float();
          continue;
        case 21:
          if (tag !== 170) {
            break;
          }
          message.vectors.push(Vectors.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      query: isSet(object.query) ? globalThis.String(object.query) : '',
      properties: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.properties)
        ? object.properties.map((e) => globalThis.String(e))
        : [],
      vector: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.vector)
        ? object.vector.map((e) => globalThis.Number(e))
        : [],
      alpha: isSet(object.alpha) ? globalThis.Number(object.alpha) : 0,
      fusionType: isSet(object.fusionType) ? hybrid_FusionTypeFromJSON(object.fusionType) : 0,
      vectorBytes: isSet(object.vectorBytes) ? bytesFromBase64(object.vectorBytes) : new Uint8Array(0),
      targetVectors: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.targetVectors
      )
        ? object.targetVectors.map((e) => globalThis.String(e))
        : [],
      nearText: isSet(object.nearText) ? NearTextSearch.fromJSON(object.nearText) : undefined,
      nearVector: isSet(object.nearVector) ? NearVector.fromJSON(object.nearVector) : undefined,
      targets: isSet(object.targets) ? Targets.fromJSON(object.targets) : undefined,
      bm25SearchOperator: isSet(object.bm25SearchOperator)
        ? SearchOperatorOptions.fromJSON(object.bm25SearchOperator)
        : undefined,
      vectorDistance: isSet(object.vectorDistance) ? globalThis.Number(object.vectorDistance) : undefined,
      vectors: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.vectors)
        ? object.vectors.map((e) => Vectors.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a, _b, _c, _d;
    const obj = {};
    if (message.query !== '') {
      obj.query = message.query;
    }
    if ((_a = message.properties) === null || _a === void 0 ? void 0 : _a.length) {
      obj.properties = message.properties;
    }
    if ((_b = message.vector) === null || _b === void 0 ? void 0 : _b.length) {
      obj.vector = message.vector;
    }
    if (message.alpha !== 0) {
      obj.alpha = message.alpha;
    }
    if (message.fusionType !== 0) {
      obj.fusionType = hybrid_FusionTypeToJSON(message.fusionType);
    }
    if (message.vectorBytes.length !== 0) {
      obj.vectorBytes = base64FromBytes(message.vectorBytes);
    }
    if ((_c = message.targetVectors) === null || _c === void 0 ? void 0 : _c.length) {
      obj.targetVectors = message.targetVectors;
    }
    if (message.nearText !== undefined) {
      obj.nearText = NearTextSearch.toJSON(message.nearText);
    }
    if (message.nearVector !== undefined) {
      obj.nearVector = NearVector.toJSON(message.nearVector);
    }
    if (message.targets !== undefined) {
      obj.targets = Targets.toJSON(message.targets);
    }
    if (message.bm25SearchOperator !== undefined) {
      obj.bm25SearchOperator = SearchOperatorOptions.toJSON(message.bm25SearchOperator);
    }
    if (message.vectorDistance !== undefined) {
      obj.vectorDistance = message.vectorDistance;
    }
    if ((_d = message.vectors) === null || _d === void 0 ? void 0 : _d.length) {
      obj.vectors = message.vectors.map((e) => Vectors.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return Hybrid.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j;
    const message = createBaseHybrid();
    message.query = (_a = object.query) !== null && _a !== void 0 ? _a : '';
    message.properties =
      ((_b = object.properties) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
    message.vector = ((_c = object.vector) === null || _c === void 0 ? void 0 : _c.map((e) => e)) || [];
    message.alpha = (_d = object.alpha) !== null && _d !== void 0 ? _d : 0;
    message.fusionType = (_e = object.fusionType) !== null && _e !== void 0 ? _e : 0;
    message.vectorBytes = (_f = object.vectorBytes) !== null && _f !== void 0 ? _f : new Uint8Array(0);
    message.targetVectors =
      ((_g = object.targetVectors) === null || _g === void 0 ? void 0 : _g.map((e) => e)) || [];
    message.nearText =
      object.nearText !== undefined && object.nearText !== null
        ? NearTextSearch.fromPartial(object.nearText)
        : undefined;
    message.nearVector =
      object.nearVector !== undefined && object.nearVector !== null
        ? NearVector.fromPartial(object.nearVector)
        : undefined;
    message.targets =
      object.targets !== undefined && object.targets !== null
        ? Targets.fromPartial(object.targets)
        : undefined;
    message.bm25SearchOperator =
      object.bm25SearchOperator !== undefined && object.bm25SearchOperator !== null
        ? SearchOperatorOptions.fromPartial(object.bm25SearchOperator)
        : undefined;
    message.vectorDistance = (_h = object.vectorDistance) !== null && _h !== void 0 ? _h : undefined;
    message.vectors =
      ((_j = object.vectors) === null || _j === void 0 ? void 0 : _j.map((e) => Vectors.fromPartial(e))) ||
      [];
    return message;
  },
};
function createBaseNearVector() {
  return {
    vector: [],
    certainty: undefined,
    distance: undefined,
    vectorBytes: new Uint8Array(0),
    targetVectors: [],
    targets: undefined,
    vectorPerTarget: {},
    vectorForTargets: [],
    vectors: [],
  };
}
export const NearVector = {
  encode(message, writer = _m0.Writer.create()) {
    writer.uint32(10).fork();
    for (const v of message.vector) {
      writer.float(v);
    }
    writer.ldelim();
    if (message.certainty !== undefined) {
      writer.uint32(17).double(message.certainty);
    }
    if (message.distance !== undefined) {
      writer.uint32(25).double(message.distance);
    }
    if (message.vectorBytes.length !== 0) {
      writer.uint32(34).bytes(message.vectorBytes);
    }
    for (const v of message.targetVectors) {
      writer.uint32(42).string(v);
    }
    if (message.targets !== undefined) {
      Targets.encode(message.targets, writer.uint32(50).fork()).ldelim();
    }
    Object.entries(message.vectorPerTarget).forEach(([key, value]) => {
      NearVector_VectorPerTargetEntry.encode({ key: key, value }, writer.uint32(58).fork()).ldelim();
    });
    for (const v of message.vectorForTargets) {
      VectorForTarget.encode(v, writer.uint32(66).fork()).ldelim();
    }
    for (const v of message.vectors) {
      Vectors.encode(v, writer.uint32(74).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearVector();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 13) {
            message.vector.push(reader.float());
            continue;
          }
          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.vector.push(reader.float());
            }
            continue;
          }
          break;
        case 2:
          if (tag !== 17) {
            break;
          }
          message.certainty = reader.double();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.distance = reader.double();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.vectorBytes = reader.bytes();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.targetVectors.push(reader.string());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.targets = Targets.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          const entry7 = NearVector_VectorPerTargetEntry.decode(reader, reader.uint32());
          if (entry7.value !== undefined) {
            message.vectorPerTarget[entry7.key] = entry7.value;
          }
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }
          message.vectorForTargets.push(VectorForTarget.decode(reader, reader.uint32()));
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.vectors.push(Vectors.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      vector: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.vector)
        ? object.vector.map((e) => globalThis.Number(e))
        : [],
      certainty: isSet(object.certainty) ? globalThis.Number(object.certainty) : undefined,
      distance: isSet(object.distance) ? globalThis.Number(object.distance) : undefined,
      vectorBytes: isSet(object.vectorBytes) ? bytesFromBase64(object.vectorBytes) : new Uint8Array(0),
      targetVectors: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.targetVectors
      )
        ? object.targetVectors.map((e) => globalThis.String(e))
        : [],
      targets: isSet(object.targets) ? Targets.fromJSON(object.targets) : undefined,
      vectorPerTarget: isObject(object.vectorPerTarget)
        ? Object.entries(object.vectorPerTarget).reduce((acc, [key, value]) => {
            acc[key] = bytesFromBase64(value);
            return acc;
          }, {})
        : {},
      vectorForTargets: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.vectorForTargets
      )
        ? object.vectorForTargets.map((e) => VectorForTarget.fromJSON(e))
        : [],
      vectors: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.vectors)
        ? object.vectors.map((e) => Vectors.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a, _b, _c, _d;
    const obj = {};
    if ((_a = message.vector) === null || _a === void 0 ? void 0 : _a.length) {
      obj.vector = message.vector;
    }
    if (message.certainty !== undefined) {
      obj.certainty = message.certainty;
    }
    if (message.distance !== undefined) {
      obj.distance = message.distance;
    }
    if (message.vectorBytes.length !== 0) {
      obj.vectorBytes = base64FromBytes(message.vectorBytes);
    }
    if ((_b = message.targetVectors) === null || _b === void 0 ? void 0 : _b.length) {
      obj.targetVectors = message.targetVectors;
    }
    if (message.targets !== undefined) {
      obj.targets = Targets.toJSON(message.targets);
    }
    if (message.vectorPerTarget) {
      const entries = Object.entries(message.vectorPerTarget);
      if (entries.length > 0) {
        obj.vectorPerTarget = {};
        entries.forEach(([k, v]) => {
          obj.vectorPerTarget[k] = base64FromBytes(v);
        });
      }
    }
    if ((_c = message.vectorForTargets) === null || _c === void 0 ? void 0 : _c.length) {
      obj.vectorForTargets = message.vectorForTargets.map((e) => VectorForTarget.toJSON(e));
    }
    if ((_d = message.vectors) === null || _d === void 0 ? void 0 : _d.length) {
      obj.vectors = message.vectors.map((e) => Vectors.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return NearVector.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    const message = createBaseNearVector();
    message.vector = ((_a = object.vector) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    message.certainty = (_b = object.certainty) !== null && _b !== void 0 ? _b : undefined;
    message.distance = (_c = object.distance) !== null && _c !== void 0 ? _c : undefined;
    message.vectorBytes = (_d = object.vectorBytes) !== null && _d !== void 0 ? _d : new Uint8Array(0);
    message.targetVectors =
      ((_e = object.targetVectors) === null || _e === void 0 ? void 0 : _e.map((e) => e)) || [];
    message.targets =
      object.targets !== undefined && object.targets !== null
        ? Targets.fromPartial(object.targets)
        : undefined;
    message.vectorPerTarget = Object.entries(
      (_f = object.vectorPerTarget) !== null && _f !== void 0 ? _f : {}
    ).reduce((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {});
    message.vectorForTargets =
      ((_g = object.vectorForTargets) === null || _g === void 0
        ? void 0
        : _g.map((e) => VectorForTarget.fromPartial(e))) || [];
    message.vectors =
      ((_h = object.vectors) === null || _h === void 0 ? void 0 : _h.map((e) => Vectors.fromPartial(e))) ||
      [];
    return message;
  },
};
function createBaseNearVector_VectorPerTargetEntry() {
  return { key: '', value: new Uint8Array(0) };
}
export const NearVector_VectorPerTargetEntry = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.key !== '') {
      writer.uint32(10).string(message.key);
    }
    if (message.value.length !== 0) {
      writer.uint32(18).bytes(message.value);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearVector_VectorPerTargetEntry();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.key = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.value = reader.bytes();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? bytesFromBase64(object.value) : new Uint8Array(0),
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.key !== '') {
      obj.key = message.key;
    }
    if (message.value.length !== 0) {
      obj.value = base64FromBytes(message.value);
    }
    return obj;
  },
  create(base) {
    return NearVector_VectorPerTargetEntry.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseNearVector_VectorPerTargetEntry();
    message.key = (_a = object.key) !== null && _a !== void 0 ? _a : '';
    message.value = (_b = object.value) !== null && _b !== void 0 ? _b : new Uint8Array(0);
    return message;
  },
};
function createBaseNearObject() {
  return { id: '', certainty: undefined, distance: undefined, targetVectors: [], targets: undefined };
}
export const NearObject = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.id !== '') {
      writer.uint32(10).string(message.id);
    }
    if (message.certainty !== undefined) {
      writer.uint32(17).double(message.certainty);
    }
    if (message.distance !== undefined) {
      writer.uint32(25).double(message.distance);
    }
    for (const v of message.targetVectors) {
      writer.uint32(34).string(v);
    }
    if (message.targets !== undefined) {
      Targets.encode(message.targets, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearObject();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }
          message.certainty = reader.double();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.distance = reader.double();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.targetVectors.push(reader.string());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.targets = Targets.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      certainty: isSet(object.certainty) ? globalThis.Number(object.certainty) : undefined,
      distance: isSet(object.distance) ? globalThis.Number(object.distance) : undefined,
      targetVectors: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.targetVectors
      )
        ? object.targetVectors.map((e) => globalThis.String(e))
        : [],
      targets: isSet(object.targets) ? Targets.fromJSON(object.targets) : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.id !== '') {
      obj.id = message.id;
    }
    if (message.certainty !== undefined) {
      obj.certainty = message.certainty;
    }
    if (message.distance !== undefined) {
      obj.distance = message.distance;
    }
    if ((_a = message.targetVectors) === null || _a === void 0 ? void 0 : _a.length) {
      obj.targetVectors = message.targetVectors;
    }
    if (message.targets !== undefined) {
      obj.targets = Targets.toJSON(message.targets);
    }
    return obj;
  },
  create(base) {
    return NearObject.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseNearObject();
    message.id = (_a = object.id) !== null && _a !== void 0 ? _a : '';
    message.certainty = (_b = object.certainty) !== null && _b !== void 0 ? _b : undefined;
    message.distance = (_c = object.distance) !== null && _c !== void 0 ? _c : undefined;
    message.targetVectors =
      ((_d = object.targetVectors) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
    message.targets =
      object.targets !== undefined && object.targets !== null
        ? Targets.fromPartial(object.targets)
        : undefined;
    return message;
  },
};
function createBaseNearTextSearch() {
  return {
    query: [],
    certainty: undefined,
    distance: undefined,
    moveTo: undefined,
    moveAway: undefined,
    targetVectors: [],
    targets: undefined,
  };
}
export const NearTextSearch = {
  encode(message, writer = _m0.Writer.create()) {
    for (const v of message.query) {
      writer.uint32(10).string(v);
    }
    if (message.certainty !== undefined) {
      writer.uint32(17).double(message.certainty);
    }
    if (message.distance !== undefined) {
      writer.uint32(25).double(message.distance);
    }
    if (message.moveTo !== undefined) {
      NearTextSearch_Move.encode(message.moveTo, writer.uint32(34).fork()).ldelim();
    }
    if (message.moveAway !== undefined) {
      NearTextSearch_Move.encode(message.moveAway, writer.uint32(42).fork()).ldelim();
    }
    for (const v of message.targetVectors) {
      writer.uint32(50).string(v);
    }
    if (message.targets !== undefined) {
      Targets.encode(message.targets, writer.uint32(58).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearTextSearch();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.query.push(reader.string());
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }
          message.certainty = reader.double();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.distance = reader.double();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.moveTo = NearTextSearch_Move.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.moveAway = NearTextSearch_Move.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.targetVectors.push(reader.string());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.targets = Targets.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      query: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.query)
        ? object.query.map((e) => globalThis.String(e))
        : [],
      certainty: isSet(object.certainty) ? globalThis.Number(object.certainty) : undefined,
      distance: isSet(object.distance) ? globalThis.Number(object.distance) : undefined,
      moveTo: isSet(object.moveTo) ? NearTextSearch_Move.fromJSON(object.moveTo) : undefined,
      moveAway: isSet(object.moveAway) ? NearTextSearch_Move.fromJSON(object.moveAway) : undefined,
      targetVectors: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.targetVectors
      )
        ? object.targetVectors.map((e) => globalThis.String(e))
        : [],
      targets: isSet(object.targets) ? Targets.fromJSON(object.targets) : undefined,
    };
  },
  toJSON(message) {
    var _a, _b;
    const obj = {};
    if ((_a = message.query) === null || _a === void 0 ? void 0 : _a.length) {
      obj.query = message.query;
    }
    if (message.certainty !== undefined) {
      obj.certainty = message.certainty;
    }
    if (message.distance !== undefined) {
      obj.distance = message.distance;
    }
    if (message.moveTo !== undefined) {
      obj.moveTo = NearTextSearch_Move.toJSON(message.moveTo);
    }
    if (message.moveAway !== undefined) {
      obj.moveAway = NearTextSearch_Move.toJSON(message.moveAway);
    }
    if ((_b = message.targetVectors) === null || _b === void 0 ? void 0 : _b.length) {
      obj.targetVectors = message.targetVectors;
    }
    if (message.targets !== undefined) {
      obj.targets = Targets.toJSON(message.targets);
    }
    return obj;
  },
  create(base) {
    return NearTextSearch.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseNearTextSearch();
    message.query = ((_a = object.query) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    message.certainty = (_b = object.certainty) !== null && _b !== void 0 ? _b : undefined;
    message.distance = (_c = object.distance) !== null && _c !== void 0 ? _c : undefined;
    message.moveTo =
      object.moveTo !== undefined && object.moveTo !== null
        ? NearTextSearch_Move.fromPartial(object.moveTo)
        : undefined;
    message.moveAway =
      object.moveAway !== undefined && object.moveAway !== null
        ? NearTextSearch_Move.fromPartial(object.moveAway)
        : undefined;
    message.targetVectors =
      ((_d = object.targetVectors) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
    message.targets =
      object.targets !== undefined && object.targets !== null
        ? Targets.fromPartial(object.targets)
        : undefined;
    return message;
  },
};
function createBaseNearTextSearch_Move() {
  return { force: 0, concepts: [], uuids: [] };
}
export const NearTextSearch_Move = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.force !== 0) {
      writer.uint32(13).float(message.force);
    }
    for (const v of message.concepts) {
      writer.uint32(18).string(v);
    }
    for (const v of message.uuids) {
      writer.uint32(26).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearTextSearch_Move();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 13) {
            break;
          }
          message.force = reader.float();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.concepts.push(reader.string());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.uuids.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      force: isSet(object.force) ? globalThis.Number(object.force) : 0,
      concepts: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.concepts)
        ? object.concepts.map((e) => globalThis.String(e))
        : [],
      uuids: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.uuids)
        ? object.uuids.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a, _b;
    const obj = {};
    if (message.force !== 0) {
      obj.force = message.force;
    }
    if ((_a = message.concepts) === null || _a === void 0 ? void 0 : _a.length) {
      obj.concepts = message.concepts;
    }
    if ((_b = message.uuids) === null || _b === void 0 ? void 0 : _b.length) {
      obj.uuids = message.uuids;
    }
    return obj;
  },
  create(base) {
    return NearTextSearch_Move.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseNearTextSearch_Move();
    message.force = (_a = object.force) !== null && _a !== void 0 ? _a : 0;
    message.concepts = ((_b = object.concepts) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
    message.uuids = ((_c = object.uuids) === null || _c === void 0 ? void 0 : _c.map((e) => e)) || [];
    return message;
  },
};
function createBaseNearImageSearch() {
  return { image: '', certainty: undefined, distance: undefined, targetVectors: [], targets: undefined };
}
export const NearImageSearch = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.image !== '') {
      writer.uint32(10).string(message.image);
    }
    if (message.certainty !== undefined) {
      writer.uint32(17).double(message.certainty);
    }
    if (message.distance !== undefined) {
      writer.uint32(25).double(message.distance);
    }
    for (const v of message.targetVectors) {
      writer.uint32(34).string(v);
    }
    if (message.targets !== undefined) {
      Targets.encode(message.targets, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearImageSearch();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.image = reader.string();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }
          message.certainty = reader.double();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.distance = reader.double();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.targetVectors.push(reader.string());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.targets = Targets.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      image: isSet(object.image) ? globalThis.String(object.image) : '',
      certainty: isSet(object.certainty) ? globalThis.Number(object.certainty) : undefined,
      distance: isSet(object.distance) ? globalThis.Number(object.distance) : undefined,
      targetVectors: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.targetVectors
      )
        ? object.targetVectors.map((e) => globalThis.String(e))
        : [],
      targets: isSet(object.targets) ? Targets.fromJSON(object.targets) : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.image !== '') {
      obj.image = message.image;
    }
    if (message.certainty !== undefined) {
      obj.certainty = message.certainty;
    }
    if (message.distance !== undefined) {
      obj.distance = message.distance;
    }
    if ((_a = message.targetVectors) === null || _a === void 0 ? void 0 : _a.length) {
      obj.targetVectors = message.targetVectors;
    }
    if (message.targets !== undefined) {
      obj.targets = Targets.toJSON(message.targets);
    }
    return obj;
  },
  create(base) {
    return NearImageSearch.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseNearImageSearch();
    message.image = (_a = object.image) !== null && _a !== void 0 ? _a : '';
    message.certainty = (_b = object.certainty) !== null && _b !== void 0 ? _b : undefined;
    message.distance = (_c = object.distance) !== null && _c !== void 0 ? _c : undefined;
    message.targetVectors =
      ((_d = object.targetVectors) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
    message.targets =
      object.targets !== undefined && object.targets !== null
        ? Targets.fromPartial(object.targets)
        : undefined;
    return message;
  },
};
function createBaseNearAudioSearch() {
  return { audio: '', certainty: undefined, distance: undefined, targetVectors: [], targets: undefined };
}
export const NearAudioSearch = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.audio !== '') {
      writer.uint32(10).string(message.audio);
    }
    if (message.certainty !== undefined) {
      writer.uint32(17).double(message.certainty);
    }
    if (message.distance !== undefined) {
      writer.uint32(25).double(message.distance);
    }
    for (const v of message.targetVectors) {
      writer.uint32(34).string(v);
    }
    if (message.targets !== undefined) {
      Targets.encode(message.targets, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearAudioSearch();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.audio = reader.string();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }
          message.certainty = reader.double();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.distance = reader.double();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.targetVectors.push(reader.string());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.targets = Targets.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      audio: isSet(object.audio) ? globalThis.String(object.audio) : '',
      certainty: isSet(object.certainty) ? globalThis.Number(object.certainty) : undefined,
      distance: isSet(object.distance) ? globalThis.Number(object.distance) : undefined,
      targetVectors: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.targetVectors
      )
        ? object.targetVectors.map((e) => globalThis.String(e))
        : [],
      targets: isSet(object.targets) ? Targets.fromJSON(object.targets) : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.audio !== '') {
      obj.audio = message.audio;
    }
    if (message.certainty !== undefined) {
      obj.certainty = message.certainty;
    }
    if (message.distance !== undefined) {
      obj.distance = message.distance;
    }
    if ((_a = message.targetVectors) === null || _a === void 0 ? void 0 : _a.length) {
      obj.targetVectors = message.targetVectors;
    }
    if (message.targets !== undefined) {
      obj.targets = Targets.toJSON(message.targets);
    }
    return obj;
  },
  create(base) {
    return NearAudioSearch.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseNearAudioSearch();
    message.audio = (_a = object.audio) !== null && _a !== void 0 ? _a : '';
    message.certainty = (_b = object.certainty) !== null && _b !== void 0 ? _b : undefined;
    message.distance = (_c = object.distance) !== null && _c !== void 0 ? _c : undefined;
    message.targetVectors =
      ((_d = object.targetVectors) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
    message.targets =
      object.targets !== undefined && object.targets !== null
        ? Targets.fromPartial(object.targets)
        : undefined;
    return message;
  },
};
function createBaseNearVideoSearch() {
  return { video: '', certainty: undefined, distance: undefined, targetVectors: [], targets: undefined };
}
export const NearVideoSearch = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.video !== '') {
      writer.uint32(10).string(message.video);
    }
    if (message.certainty !== undefined) {
      writer.uint32(17).double(message.certainty);
    }
    if (message.distance !== undefined) {
      writer.uint32(25).double(message.distance);
    }
    for (const v of message.targetVectors) {
      writer.uint32(34).string(v);
    }
    if (message.targets !== undefined) {
      Targets.encode(message.targets, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearVideoSearch();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.video = reader.string();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }
          message.certainty = reader.double();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.distance = reader.double();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.targetVectors.push(reader.string());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.targets = Targets.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      video: isSet(object.video) ? globalThis.String(object.video) : '',
      certainty: isSet(object.certainty) ? globalThis.Number(object.certainty) : undefined,
      distance: isSet(object.distance) ? globalThis.Number(object.distance) : undefined,
      targetVectors: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.targetVectors
      )
        ? object.targetVectors.map((e) => globalThis.String(e))
        : [],
      targets: isSet(object.targets) ? Targets.fromJSON(object.targets) : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.video !== '') {
      obj.video = message.video;
    }
    if (message.certainty !== undefined) {
      obj.certainty = message.certainty;
    }
    if (message.distance !== undefined) {
      obj.distance = message.distance;
    }
    if ((_a = message.targetVectors) === null || _a === void 0 ? void 0 : _a.length) {
      obj.targetVectors = message.targetVectors;
    }
    if (message.targets !== undefined) {
      obj.targets = Targets.toJSON(message.targets);
    }
    return obj;
  },
  create(base) {
    return NearVideoSearch.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseNearVideoSearch();
    message.video = (_a = object.video) !== null && _a !== void 0 ? _a : '';
    message.certainty = (_b = object.certainty) !== null && _b !== void 0 ? _b : undefined;
    message.distance = (_c = object.distance) !== null && _c !== void 0 ? _c : undefined;
    message.targetVectors =
      ((_d = object.targetVectors) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
    message.targets =
      object.targets !== undefined && object.targets !== null
        ? Targets.fromPartial(object.targets)
        : undefined;
    return message;
  },
};
function createBaseNearDepthSearch() {
  return { depth: '', certainty: undefined, distance: undefined, targetVectors: [], targets: undefined };
}
export const NearDepthSearch = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.depth !== '') {
      writer.uint32(10).string(message.depth);
    }
    if (message.certainty !== undefined) {
      writer.uint32(17).double(message.certainty);
    }
    if (message.distance !== undefined) {
      writer.uint32(25).double(message.distance);
    }
    for (const v of message.targetVectors) {
      writer.uint32(34).string(v);
    }
    if (message.targets !== undefined) {
      Targets.encode(message.targets, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearDepthSearch();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.depth = reader.string();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }
          message.certainty = reader.double();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.distance = reader.double();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.targetVectors.push(reader.string());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.targets = Targets.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      depth: isSet(object.depth) ? globalThis.String(object.depth) : '',
      certainty: isSet(object.certainty) ? globalThis.Number(object.certainty) : undefined,
      distance: isSet(object.distance) ? globalThis.Number(object.distance) : undefined,
      targetVectors: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.targetVectors
      )
        ? object.targetVectors.map((e) => globalThis.String(e))
        : [],
      targets: isSet(object.targets) ? Targets.fromJSON(object.targets) : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.depth !== '') {
      obj.depth = message.depth;
    }
    if (message.certainty !== undefined) {
      obj.certainty = message.certainty;
    }
    if (message.distance !== undefined) {
      obj.distance = message.distance;
    }
    if ((_a = message.targetVectors) === null || _a === void 0 ? void 0 : _a.length) {
      obj.targetVectors = message.targetVectors;
    }
    if (message.targets !== undefined) {
      obj.targets = Targets.toJSON(message.targets);
    }
    return obj;
  },
  create(base) {
    return NearDepthSearch.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseNearDepthSearch();
    message.depth = (_a = object.depth) !== null && _a !== void 0 ? _a : '';
    message.certainty = (_b = object.certainty) !== null && _b !== void 0 ? _b : undefined;
    message.distance = (_c = object.distance) !== null && _c !== void 0 ? _c : undefined;
    message.targetVectors =
      ((_d = object.targetVectors) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
    message.targets =
      object.targets !== undefined && object.targets !== null
        ? Targets.fromPartial(object.targets)
        : undefined;
    return message;
  },
};
function createBaseNearThermalSearch() {
  return { thermal: '', certainty: undefined, distance: undefined, targetVectors: [], targets: undefined };
}
export const NearThermalSearch = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.thermal !== '') {
      writer.uint32(10).string(message.thermal);
    }
    if (message.certainty !== undefined) {
      writer.uint32(17).double(message.certainty);
    }
    if (message.distance !== undefined) {
      writer.uint32(25).double(message.distance);
    }
    for (const v of message.targetVectors) {
      writer.uint32(34).string(v);
    }
    if (message.targets !== undefined) {
      Targets.encode(message.targets, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearThermalSearch();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.thermal = reader.string();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }
          message.certainty = reader.double();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.distance = reader.double();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.targetVectors.push(reader.string());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.targets = Targets.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      thermal: isSet(object.thermal) ? globalThis.String(object.thermal) : '',
      certainty: isSet(object.certainty) ? globalThis.Number(object.certainty) : undefined,
      distance: isSet(object.distance) ? globalThis.Number(object.distance) : undefined,
      targetVectors: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.targetVectors
      )
        ? object.targetVectors.map((e) => globalThis.String(e))
        : [],
      targets: isSet(object.targets) ? Targets.fromJSON(object.targets) : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.thermal !== '') {
      obj.thermal = message.thermal;
    }
    if (message.certainty !== undefined) {
      obj.certainty = message.certainty;
    }
    if (message.distance !== undefined) {
      obj.distance = message.distance;
    }
    if ((_a = message.targetVectors) === null || _a === void 0 ? void 0 : _a.length) {
      obj.targetVectors = message.targetVectors;
    }
    if (message.targets !== undefined) {
      obj.targets = Targets.toJSON(message.targets);
    }
    return obj;
  },
  create(base) {
    return NearThermalSearch.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseNearThermalSearch();
    message.thermal = (_a = object.thermal) !== null && _a !== void 0 ? _a : '';
    message.certainty = (_b = object.certainty) !== null && _b !== void 0 ? _b : undefined;
    message.distance = (_c = object.distance) !== null && _c !== void 0 ? _c : undefined;
    message.targetVectors =
      ((_d = object.targetVectors) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
    message.targets =
      object.targets !== undefined && object.targets !== null
        ? Targets.fromPartial(object.targets)
        : undefined;
    return message;
  },
};
function createBaseNearIMUSearch() {
  return { imu: '', certainty: undefined, distance: undefined, targetVectors: [], targets: undefined };
}
export const NearIMUSearch = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.imu !== '') {
      writer.uint32(10).string(message.imu);
    }
    if (message.certainty !== undefined) {
      writer.uint32(17).double(message.certainty);
    }
    if (message.distance !== undefined) {
      writer.uint32(25).double(message.distance);
    }
    for (const v of message.targetVectors) {
      writer.uint32(34).string(v);
    }
    if (message.targets !== undefined) {
      Targets.encode(message.targets, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseNearIMUSearch();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.imu = reader.string();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }
          message.certainty = reader.double();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }
          message.distance = reader.double();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.targetVectors.push(reader.string());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.targets = Targets.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      imu: isSet(object.imu) ? globalThis.String(object.imu) : '',
      certainty: isSet(object.certainty) ? globalThis.Number(object.certainty) : undefined,
      distance: isSet(object.distance) ? globalThis.Number(object.distance) : undefined,
      targetVectors: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.targetVectors
      )
        ? object.targetVectors.map((e) => globalThis.String(e))
        : [],
      targets: isSet(object.targets) ? Targets.fromJSON(object.targets) : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.imu !== '') {
      obj.imu = message.imu;
    }
    if (message.certainty !== undefined) {
      obj.certainty = message.certainty;
    }
    if (message.distance !== undefined) {
      obj.distance = message.distance;
    }
    if ((_a = message.targetVectors) === null || _a === void 0 ? void 0 : _a.length) {
      obj.targetVectors = message.targetVectors;
    }
    if (message.targets !== undefined) {
      obj.targets = Targets.toJSON(message.targets);
    }
    return obj;
  },
  create(base) {
    return NearIMUSearch.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseNearIMUSearch();
    message.imu = (_a = object.imu) !== null && _a !== void 0 ? _a : '';
    message.certainty = (_b = object.certainty) !== null && _b !== void 0 ? _b : undefined;
    message.distance = (_c = object.distance) !== null && _c !== void 0 ? _c : undefined;
    message.targetVectors =
      ((_d = object.targetVectors) === null || _d === void 0 ? void 0 : _d.map((e) => e)) || [];
    message.targets =
      object.targets !== undefined && object.targets !== null
        ? Targets.fromPartial(object.targets)
        : undefined;
    return message;
  },
};
function createBaseBM25() {
  return { query: '', properties: [], searchOperator: undefined };
}
export const BM25 = {
  encode(message, writer = _m0.Writer.create()) {
    if (message.query !== '') {
      writer.uint32(10).string(message.query);
    }
    for (const v of message.properties) {
      writer.uint32(18).string(v);
    }
    if (message.searchOperator !== undefined) {
      SearchOperatorOptions.encode(message.searchOperator, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBM25();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.query = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.properties.push(reader.string());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.searchOperator = SearchOperatorOptions.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      query: isSet(object.query) ? globalThis.String(object.query) : '',
      properties: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.properties)
        ? object.properties.map((e) => globalThis.String(e))
        : [],
      searchOperator: isSet(object.searchOperator)
        ? SearchOperatorOptions.fromJSON(object.searchOperator)
        : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.query !== '') {
      obj.query = message.query;
    }
    if ((_a = message.properties) === null || _a === void 0 ? void 0 : _a.length) {
      obj.properties = message.properties;
    }
    if (message.searchOperator !== undefined) {
      obj.searchOperator = SearchOperatorOptions.toJSON(message.searchOperator);
    }
    return obj;
  },
  create(base) {
    return BM25.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseBM25();
    message.query = (_a = object.query) !== null && _a !== void 0 ? _a : '';
    message.properties =
      ((_b = object.properties) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
    message.searchOperator =
      object.searchOperator !== undefined && object.searchOperator !== null
        ? SearchOperatorOptions.fromPartial(object.searchOperator)
        : undefined;
    return message;
  },
};
function bytesFromBase64(b64) {
  if (globalThis.Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}
function base64FromBytes(arr) {
  if (globalThis.Buffer) {
    return globalThis.Buffer.from(arr).toString('base64');
  } else {
    const bin = [];
    arr.forEach((byte) => {
      bin.push(globalThis.String.fromCharCode(byte));
    });
    return globalThis.btoa(bin.join(''));
  }
}
function isObject(value) {
  return typeof value === 'object' && value !== null;
}
function isSet(value) {
  return value !== null && value !== undefined;
}
