{"/Users/<USER>/winston-transport/index.js": {"path": "/Users/<USER>/winston-transport/index.js", "statementMap": {"0": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 37}}, "1": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 59}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1}, "f": {}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "8b988309d802712672b7bff507361099a67bc431", "contentHash": "d5babdcb1bbe43b5d0fd4acf7b41f817fcd458ad896ae7085433b14c7564e890"}, "/Users/<USER>/winston-transport/modern.js": {"path": "/Users/<USER>/winston-transport/modern.js", "statementMap": {"0": {"start": {"line": 3, "column": 13}, "end": {"line": 3, "column": 28}}, "1": {"start": {"line": 4, "column": 17}, "end": {"line": 4, "column": 67}}, "2": {"start": {"line": 5, "column": 18}, "end": {"line": 5, "column": 40}}, "3": {"start": {"line": 18, "column": 24}, "end": {"line": 54, "column": 1}}, "4": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 82}}, "5": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 31}}, "6": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 29}}, "7": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 51}}, "8": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 51}}, "9": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 31}}, "10": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 42}}, "11": {"start": {"line": 27, "column": 19}, "end": {"line": 27, "column": 42}}, "12": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 45}}, "13": {"start": {"line": 28, "column": 20}, "end": {"line": 28, "column": 45}}, "14": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 48}}, "15": {"start": {"line": 29, "column": 21}, "end": {"line": 29, "column": 48}}, "16": {"start": {"line": 32, "column": 2}, "end": {"line": 39, "column": 5}}, "17": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 32}}, "18": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 25}}, "19": {"start": {"line": 42, "column": 2}, "end": {"line": 53, "column": 5}}, "20": {"start": {"line": 47, "column": 4}, "end": {"line": 52, "column": 5}}, "21": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 25}}, "22": {"start": {"line": 49, "column": 6}, "end": {"line": 51, "column": 7}}, "23": {"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 21}}, "24": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 41}}, "25": {"start": {"line": 69, "column": 0}, "end": {"line": 107, "column": 2}}, "26": {"start": {"line": 70, "column": 2}, "end": {"line": 72, "column": 3}}, "27": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 26}}, "28": {"start": {"line": 78, "column": 16}, "end": {"line": 78, "column": 64}}, "29": {"start": {"line": 80, "column": 2}, "end": {"line": 104, "column": 3}}, "30": {"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, "31": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 38}}, "32": {"start": {"line": 90, "column": 4}, "end": {"line": 94, "column": 5}}, "33": {"start": {"line": 91, "column": 6}, "end": {"line": 91, "column": 88}}, "34": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 21}}, "35": {"start": {"line": 96, "column": 4}, "end": {"line": 101, "column": 5}}, "36": {"start": {"line": 98, "column": 6}, "end": {"line": 98, "column": 17}}, "37": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 35}}, "38": {"start": {"line": 99, "column": 20}, "end": {"line": 99, "column": 35}}, "39": {"start": {"line": 100, "column": 6}, "end": {"line": 100, "column": 13}}, "40": {"start": {"line": 103, "column": 4}, "end": {"line": 103, "column": 43}}, "41": {"start": {"line": 105, "column": 2}, "end": {"line": 105, "column": 35}}, "42": {"start": {"line": 106, "column": 2}, "end": {"line": 106, "column": 24}}, "43": {"start": {"line": 117, "column": 0}, "end": {"line": 166, "column": 2}}, "44": {"start": {"line": 118, "column": 2}, "end": {"line": 128, "column": 3}}, "45": {"start": {"line": 119, "column": 18}, "end": {"line": 119, "column": 51}}, "46": {"start": {"line": 120, "column": 4}, "end": {"line": 122, "column": 5}}, "47": {"start": {"line": 121, "column": 6}, "end": {"line": 121, "column": 28}}, "48": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 38}}, "49": {"start": {"line": 130, "column": 2}, "end": {"line": 163, "column": 3}}, "50": {"start": {"line": 130, "column": 15}, "end": {"line": 130, "column": 16}}, "51": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 43}}, "52": {"start": {"line": 131, "column": 34}, "end": {"line": 131, "column": 43}}, "53": {"start": {"line": 133, "column": 4}, "end": {"line": 136, "column": 5}}, "54": {"start": {"line": 134, "column": 6}, "end": {"line": 134, "column": 52}}, "55": {"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": 15}}, "56": {"start": {"line": 143, "column": 4}, "end": {"line": 150, "column": 5}}, "57": {"start": {"line": 144, "column": 6}, "end": {"line": 147, "column": 8}}, "58": {"start": {"line": 149, "column": 6}, "end": {"line": 149, "column": 21}}, "59": {"start": {"line": 152, "column": 4}, "end": {"line": 162, "column": 5}}, "60": {"start": {"line": 154, "column": 6}, "end": {"line": 154, "column": 27}}, "61": {"start": {"line": 155, "column": 6}, "end": {"line": 159, "column": 7}}, "62": {"start": {"line": 157, "column": 8}, "end": {"line": 157, "column": 23}}, "63": {"start": {"line": 158, "column": 8}, "end": {"line": 158, "column": 23}}, "64": {"start": {"line": 161, "column": 6}, "end": {"line": 161, "column": 48}}, "65": {"start": {"line": 165, "column": 2}, "end": {"line": 165, "column": 24}}, "66": {"start": {"line": 177, "column": 0}, "end": {"line": 202, "column": 2}}, "67": {"start": {"line": 178, "column": 15}, "end": {"line": 178, "column": 26}}, "68": {"start": {"line": 179, "column": 2}, "end": {"line": 181, "column": 3}}, "69": {"start": {"line": 180, "column": 4}, "end": {"line": 180, "column": 17}}, "70": {"start": {"line": 185, "column": 16}, "end": {"line": 185, "column": 64}}, "71": {"start": {"line": 188, "column": 2}, "end": {"line": 199, "column": 3}}, "72": {"start": {"line": 196, "column": 4}, "end": {"line": 198, "column": 5}}, "73": {"start": {"line": 197, "column": 6}, "end": {"line": 197, "column": 18}}, "74": {"start": {"line": 201, "column": 2}, "end": {"line": 201, "column": 15}}, "75": {"start": {"line": 208, "column": 0}, "end": {"line": 211, "column": 2}}, "76": {"start": {"line": 210, "column": 2}, "end": {"line": 210, "column": 24}}}, "fnMap": {"0": {"name": "TransportStream", "decl": {"start": {"line": 18, "column": 50}, "end": {"line": 18, "column": 65}}, "loc": {"start": {"line": 18, "column": 80}, "end": {"line": 54, "column": 1}}, "line": 18}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 32, "column": 20}, "end": {"line": 32, "column": 21}}, "loc": {"start": {"line": 32, "column": 30}, "end": {"line": 39, "column": 3}}, "line": 32}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 42, "column": 22}, "end": {"line": 42, "column": 23}}, "loc": {"start": {"line": 42, "column": 29}, "end": {"line": 53, "column": 3}}, "line": 42}, "3": {"name": "_write", "decl": {"start": {"line": 69, "column": 44}, "end": {"line": 69, "column": 50}}, "loc": {"start": {"line": 69, "column": 72}, "end": {"line": 107, "column": 1}}, "line": 69}, "4": {"name": "_writev", "decl": {"start": {"line": 117, "column": 45}, "end": {"line": 117, "column": 52}}, "loc": {"start": {"line": 117, "column": 71}, "end": {"line": 166, "column": 1}}, "line": 117}, "5": {"name": "_accept", "decl": {"start": {"line": 177, "column": 45}, "end": {"line": 177, "column": 52}}, "loc": {"start": {"line": 177, "column": 60}, "end": {"line": 202, "column": 1}}, "line": 177}, "6": {"name": "_nop", "decl": {"start": {"line": 208, "column": 42}, "end": {"line": 208, "column": 46}}, "loc": {"start": {"line": 208, "column": 49}, "end": {"line": 211, "column": 1}}, "line": 208}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 66}, "end": {"line": 18, "column": 78}}, "type": "default-arg", "locations": [{"start": {"line": 18, "column": 76}, "end": {"line": 18, "column": 78}}], "line": 18}, "1": {"loc": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 42}}, "type": "if", "locations": [{"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 42}}, {"start": {}, "end": {}}], "line": 27}, "2": {"loc": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 45}}, "type": "if", "locations": [{"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 45}}, {"start": {}, "end": {}}], "line": 28}, "3": {"loc": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 48}}, "type": "if", "locations": [{"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 48}}, {"start": {}, "end": {}}], "line": 29}, "4": {"loc": {"start": {"line": 47, "column": 4}, "end": {"line": 52, "column": 5}}, "type": "if", "locations": [{"start": {"line": 47, "column": 4}, "end": {"line": 52, "column": 5}}, {"start": {}, "end": {}}], "line": 47}, "5": {"loc": {"start": {"line": 49, "column": 6}, "end": {"line": 51, "column": 7}}, "type": "if", "locations": [{"start": {"line": 49, "column": 6}, "end": {"line": 51, "column": 7}}, {"start": {}, "end": {}}], "line": 49}, "6": {"loc": {"start": {"line": 70, "column": 2}, "end": {"line": 72, "column": 3}}, "type": "if", "locations": [{"start": {"line": 70, "column": 2}, "end": {"line": 72, "column": 3}}, {"start": {}, "end": {}}], "line": 70}, "7": {"loc": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 17}}, {"start": {"line": 70, "column": 22}, "end": {"line": 70, "column": 45}}, {"start": {"line": 70, "column": 49}, "end": {"line": 70, "column": 71}}], "line": 70}, "8": {"loc": {"start": {"line": 78, "column": 16}, "end": {"line": 78, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 78, "column": 16}, "end": {"line": 78, "column": 26}}, {"start": {"line": 78, "column": 31}, "end": {"line": 78, "column": 42}}, {"start": {"line": 78, "column": 46}, "end": {"line": 78, "column": 63}}], "line": 78}, "9": {"loc": {"start": {"line": 80, "column": 2}, "end": {"line": 104, "column": 3}}, "type": "if", "locations": [{"start": {"line": 80, "column": 2}, "end": {"line": 104, "column": 3}}, {"start": {}, "end": {}}], "line": 80}, "10": {"loc": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": 12}}, {"start": {"line": 80, "column": 16}, "end": {"line": 80, "column": 62}}], "line": 80}, "11": {"loc": {"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, "type": "if", "locations": [{"start": {"line": 81, "column": 4}, "end": {"line": 83, "column": 5}}, {"start": {}, "end": {}}], "line": 81}, "12": {"loc": {"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 81, "column": 8}, "end": {"line": 81, "column": 12}}, {"start": {"line": 81, "column": 16}, "end": {"line": 81, "column": 28}}], "line": 81}, "13": {"loc": {"start": {"line": 96, "column": 4}, "end": {"line": 101, "column": 5}}, "type": "if", "locations": [{"start": {"line": 96, "column": 4}, "end": {"line": 101, "column": 5}}, {"start": {}, "end": {}}], "line": 96}, "14": {"loc": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 16}}, {"start": {"line": 96, "column": 20}, "end": {"line": 96, "column": 32}}], "line": 96}, "15": {"loc": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 35}}, "type": "if", "locations": [{"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 35}}, {"start": {}, "end": {}}], "line": 99}, "16": {"loc": {"start": {"line": 118, "column": 2}, "end": {"line": 128, "column": 3}}, "type": "if", "locations": [{"start": {"line": 118, "column": 2}, "end": {"line": 128, "column": 3}}, {"start": {}, "end": {}}], "line": 118}, "17": {"loc": {"start": {"line": 120, "column": 4}, "end": {"line": 122, "column": 5}}, "type": "if", "locations": [{"start": {"line": 120, "column": 4}, "end": {"line": 122, "column": 5}}, {"start": {}, "end": {}}], "line": 120}, "18": {"loc": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 43}}, "type": "if", "locations": [{"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 43}}, {"start": {}, "end": {}}], "line": 131}, "19": {"loc": {"start": {"line": 133, "column": 4}, "end": {"line": 136, "column": 5}}, "type": "if", "locations": [{"start": {"line": 133, "column": 4}, "end": {"line": 136, "column": 5}}, {"start": {}, "end": {}}], "line": 133}, "20": {"loc": {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 23}}, {"start": {"line": 133, "column": 27}, "end": {"line": 133, "column": 39}}], "line": 133}, "21": {"loc": {"start": {"line": 152, "column": 4}, "end": {"line": 162, "column": 5}}, "type": "if", "locations": [{"start": {"line": 152, "column": 4}, "end": {"line": 162, "column": 5}}, {"start": {"line": 160, "column": 11}, "end": {"line": 162, "column": 5}}], "line": 152}, "22": {"loc": {"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 32}}, "type": "binary-expr", "locations": [{"start": {"line": 152, "column": 8}, "end": {"line": 152, "column": 16}}, {"start": {"line": 152, "column": 20}, "end": {"line": 152, "column": 32}}], "line": 152}, "23": {"loc": {"start": {"line": 155, "column": 6}, "end": {"line": 159, "column": 7}}, "type": "if", "locations": [{"start": {"line": 155, "column": 6}, "end": {"line": 159, "column": 7}}, {"start": {}, "end": {}}], "line": 155}, "24": {"loc": {"start": {"line": 179, "column": 2}, "end": {"line": 181, "column": 3}}, "type": "if", "locations": [{"start": {"line": 179, "column": 2}, "end": {"line": 181, "column": 3}}, {"start": {}, "end": {}}], "line": 179}, "25": {"loc": {"start": {"line": 185, "column": 16}, "end": {"line": 185, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 185, "column": 16}, "end": {"line": 185, "column": 26}}, {"start": {"line": 185, "column": 31}, "end": {"line": 185, "column": 42}}, {"start": {"line": 185, "column": 46}, "end": {"line": 185, "column": 63}}], "line": 185}, "26": {"loc": {"start": {"line": 188, "column": 2}, "end": {"line": 199, "column": 3}}, "type": "if", "locations": [{"start": {"line": 188, "column": 2}, "end": {"line": 199, "column": 3}}, {"start": {}, "end": {}}], "line": 188}, "27": {"loc": {"start": {"line": 189, "column": 4}, "end": {"line": 191, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 189, "column": 4}, "end": {"line": 189, "column": 27}}, {"start": {"line": 190, "column": 4}, "end": {"line": 190, "column": 10}}, {"start": {"line": 191, "column": 4}, "end": {"line": 191, "column": 50}}], "line": 189}, "28": {"loc": {"start": {"line": 196, "column": 4}, "end": {"line": 198, "column": 5}}, "type": "if", "locations": [{"start": {"line": 196, "column": 4}, "end": {"line": 198, "column": 5}}, {"start": {}, "end": {}}], "line": 196}, "29": {"loc": {"start": {"line": 196, "column": 8}, "end": {"line": 196, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 196, "column": 8}, "end": {"line": 196, "column": 29}}, {"start": {"line": 196, "column": 33}, "end": {"line": 196, "column": 56}}], "line": 196}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 51, "5": 51, "6": 51, "7": 51, "8": 51, "9": 51, "10": 51, "11": 22, "12": 51, "13": 1, "14": 51, "15": 0, "16": 51, "17": 5, "18": 5, "19": 51, "20": 2, "21": 2, "22": 2, "23": 1, "24": 1, "25": 1, "26": 49, "27": 10, "28": 39, "29": 39, "30": 33, "31": 20, "32": 13, "33": 13, "34": 1, "35": 13, "36": 8, "37": 8, "38": 1, "39": 7, "40": 5, "41": 6, "42": 6, "43": 1, "44": 6, "45": 1, "46": 1, "47": 0, "48": 1, "49": 5, "50": 5, "51": 584, "52": 15, "53": 569, "54": 400, "55": 400, "56": 169, "57": 169, "58": 1, "59": 169, "60": 8, "61": 8, "62": 1, "63": 1, "64": 161, "65": 4, "66": 1, "67": 1425, "68": 1425, "69": 32, "70": 1393, "71": 1393, "72": 1240, "73": 1232, "74": 161, "75": 1, "76": 416}, "f": {"0": 51, "1": 5, "2": 2, "3": 49, "4": 6, "5": 1425, "6": 416}, "b": {"0": [1], "1": [22, 29], "2": [1, 50], "3": [0, 51], "4": [2, 0], "5": [1, 1], "6": [10, 39], "7": [49, 48, 10], "8": [39, 29, 4], "9": [33, 6], "10": [39, 14], "11": [20, 13], "12": [33, 33], "13": [8, 5], "14": [13, 12], "15": [1, 7], "16": [1, 5], "17": [0, 1], "18": [15, 569], "19": [400, 169], "20": [569, 569], "21": [8, 161], "22": [169, 168], "23": [1, 7], "24": [32, 1393], "25": [1393, 969, 0], "26": [1240, 153], "27": [1393, 1377, 408], "28": [1232, 8], "29": [1240, 1232]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "6105304c6b05abc925caa69a1dbbf1dc861d12b6", "contentHash": "06468df411844ad68c0b51de1ff44b0aeb62787ca06ca4eba491fa62d5f8804d"}, "/Users/<USER>/winston-transport/legacy.js": {"path": "/Users/<USER>/winston-transport/legacy.js", "statementMap": {"0": {"start": {"line": 3, "column": 13}, "end": {"line": 3, "column": 28}}, "1": {"start": {"line": 4, "column": 18}, "end": {"line": 4, "column": 40}}, "2": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": 43}}, "3": {"start": {"line": 15, "column": 30}, "end": {"line": 39, "column": 1}}, "4": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 38}}, "5": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 3}}, "6": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 79}}, "7": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 37}}, "8": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 53}}, "9": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 86}}, "10": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 21}}, "11": {"start": {"line": 32, "column": 4}, "end": {"line": 32, "column": 44}}, "12": {"start": {"line": 35, "column": 2}, "end": {"line": 38, "column": 3}}, "13": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": 62}}, "14": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 62}}, "15": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 54}}, "16": {"start": {"line": 54, "column": 0}, "end": {"line": 66, "column": 2}}, "17": {"start": {"line": 55, "column": 2}, "end": {"line": 57, "column": 3}}, "18": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 26}}, "19": {"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, "20": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 67}}, "21": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 17}}, "22": {"start": {"line": 76, "column": 0}, "end": {"line": 90, "column": 2}}, "23": {"start": {"line": 77, "column": 2}, "end": {"line": 87, "column": 3}}, "24": {"start": {"line": 77, "column": 15}, "end": {"line": 77, "column": 16}}, "25": {"start": {"line": 78, "column": 4}, "end": {"line": 86, "column": 5}}, "26": {"start": {"line": 79, "column": 6}, "end": {"line": 84, "column": 8}}, "27": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 27}}, "28": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": 24}}, "29": {"start": {"line": 97, "column": 0}, "end": {"line": 103, "column": 2}}, "30": {"start": {"line": 99, "column": 2}, "end": {"line": 102, "column": 16}}, "31": {"start": {"line": 110, "column": 0}, "end": {"line": 119, "column": 2}}, "32": {"start": {"line": 111, "column": 2}, "end": {"line": 113, "column": 3}}, "33": {"start": {"line": 112, "column": 4}, "end": {"line": 112, "column": 27}}, "34": {"start": {"line": 115, "column": 2}, "end": {"line": 118, "column": 3}}, "35": {"start": {"line": 116, "column": 4}, "end": {"line": 116, "column": 74}}, "36": {"start": {"line": 117, "column": 4}, "end": {"line": 117, "column": 41}}}, "fnMap": {"0": {"name": "LegacyTransportStream", "decl": {"start": {"line": 15, "column": 56}, "end": {"line": 15, "column": 77}}, "loc": {"start": {"line": 15, "column": 92}, "end": {"line": 39, "column": 1}}, "line": 15}, "1": {"name": "transportError", "decl": {"start": {"line": 31, "column": 11}, "end": {"line": 31, "column": 25}}, "loc": {"start": {"line": 31, "column": 31}, "end": {"line": 33, "column": 3}}, "line": 31}, "2": {"name": "_write", "decl": {"start": {"line": 54, "column": 50}, "end": {"line": 54, "column": 56}}, "loc": {"start": {"line": 54, "column": 78}, "end": {"line": 66, "column": 1}}, "line": 54}, "3": {"name": "_writev", "decl": {"start": {"line": 76, "column": 51}, "end": {"line": 76, "column": 58}}, "loc": {"start": {"line": 76, "column": 77}, "end": {"line": 90, "column": 1}}, "line": 76}, "4": {"name": "_deprecated", "decl": {"start": {"line": 97, "column": 55}, "end": {"line": 97, "column": 66}}, "loc": {"start": {"line": 97, "column": 69}, "end": {"line": 103, "column": 1}}, "line": 97}, "5": {"name": "close", "decl": {"start": {"line": 110, "column": 49}, "end": {"line": 110, "column": 54}}, "loc": {"start": {"line": 110, "column": 57}, "end": {"line": 119, "column": 1}}, "line": 110}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 78}, "end": {"line": 15, "column": 90}}, "type": "default-arg", "locations": [{"start": {"line": 15, "column": 88}, "end": {"line": 15, "column": 90}}], "line": 15}, "1": {"loc": {"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 3}}, "type": "if", "locations": [{"start": {"line": 17, "column": 2}, "end": {"line": 19, "column": 3}}, {"start": {}, "end": {}}], "line": 17}, "2": {"loc": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 24}}, {"start": {"line": 17, "column": 28}, "end": {"line": 17, "column": 71}}], "line": 17}, "3": {"loc": {"start": {"line": 22, "column": 15}, "end": {"line": 22, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 22, "column": 15}, "end": {"line": 22, "column": 25}}, {"start": {"line": 22, "column": 29}, "end": {"line": 22, "column": 52}}], "line": 22}, "4": {"loc": {"start": {"line": 23, "column": 26}, "end": {"line": 23, "column": 85}}, "type": "binary-expr", "locations": [{"start": {"line": 23, "column": 26}, "end": {"line": 23, "column": 47}}, {"start": {"line": 23, "column": 51}, "end": {"line": 23, "column": 85}}], "line": 23}, "5": {"loc": {"start": {"line": 35, "column": 2}, "end": {"line": 38, "column": 3}}, "type": "if", "locations": [{"start": {"line": 35, "column": 2}, "end": {"line": 38, "column": 3}}, {"start": {}, "end": {}}], "line": 35}, "6": {"loc": {"start": {"line": 55, "column": 2}, "end": {"line": 57, "column": 3}}, "type": "if", "locations": [{"start": {"line": 55, "column": 2}, "end": {"line": 57, "column": 3}}, {"start": {}, "end": {}}], "line": 55}, "7": {"loc": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 17}}, {"start": {"line": 55, "column": 22}, "end": {"line": 55, "column": 45}}, {"start": {"line": 55, "column": 49}, "end": {"line": 55, "column": 71}}], "line": 55}, "8": {"loc": {"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, "type": "if", "locations": [{"start": {"line": 61, "column": 2}, "end": {"line": 63, "column": 3}}, {"start": {}, "end": {}}], "line": 61}, "9": {"loc": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 72}}, "type": "binary-expr", "locations": [{"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 17}}, {"start": {"line": 61, "column": 21}, "end": {"line": 61, "column": 72}}], "line": 61}, "10": {"loc": {"start": {"line": 78, "column": 4}, "end": {"line": 86, "column": 5}}, "type": "if", "locations": [{"start": {"line": 78, "column": 4}, "end": {"line": 86, "column": 5}}, {"start": {}, "end": {}}], "line": 78}, "11": {"loc": {"start": {"line": 111, "column": 2}, "end": {"line": 113, "column": 3}}, "type": "if", "locations": [{"start": {"line": 111, "column": 2}, "end": {"line": 113, "column": 3}}, {"start": {}, "end": {}}], "line": 111}, "12": {"loc": {"start": {"line": 115, "column": 2}, "end": {"line": 118, "column": 3}}, "type": "if", "locations": [{"start": {"line": 115, "column": 2}, "end": {"line": 118, "column": 3}}, {"start": {}, "end": {}}], "line": 115}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 23, "5": 23, "6": 2, "7": 21, "8": 21, "9": 21, "10": 21, "11": 1, "12": 21, "13": 18, "14": 18, "15": 1, "16": 1, "17": 29, "18": 10, "19": 19, "20": 16, "21": 19, "22": 1, "23": 2, "24": 2, "25": 415, "26": 400, "27": 400, "28": 2, "29": 1, "30": 1, "31": 1, "32": 2, "33": 2, "34": 2, "35": 2, "36": 2}, "f": {"0": 23, "1": 1, "2": 29, "3": 2, "4": 1, "5": 2}, "b": {"0": [2], "1": [2, 21], "2": [23, 21], "3": [21, 19], "4": [21, 20], "5": [18, 3], "6": [10, 19], "7": [29, 28, 10], "8": [16, 3], "9": [19, 8], "10": [400, 15], "11": [2, 0], "12": [2, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "c9c54f11f0419fafc2148046d7f90605c3c51d6f", "contentHash": "5fb77817fd415f169b36d9aa3de2cc51923829333d30bc6f730b18cb3795f9a1"}}