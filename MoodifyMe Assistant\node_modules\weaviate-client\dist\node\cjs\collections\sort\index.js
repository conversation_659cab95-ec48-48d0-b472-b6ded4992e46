'use strict';
Object.defineProperty(exports, '__esModule', { value: true });
exports.Sorting = void 0;
const classes_js_1 = require('./classes.js');
Object.defineProperty(exports, 'Sorting', {
  enumerable: true,
  get: function () {
    return classes_js_1.Sorting;
  },
});
const sort = () => {
  return {
    byProperty(property, ascending = true) {
      return new classes_js_1.Sorting().byProperty(property, ascending);
    },
    byId(ascending = true) {
      return new classes_js_1.Sorting().byId(ascending);
    },
    byCreationTime(ascending = true) {
      return new classes_js_1.Sorting().byCreationTime(ascending);
    },
    byUpdateTime(ascending = true) {
      return new classes_js_1.Sorting().byUpdateTime(ascending);
    },
  };
};
exports.default = sort;
