var __awaiter =
  (this && this.__awaiter) ||
  function (thisArg, _arguments, P, generator) {
    function adopt(value) {
      return value instanceof P
        ? value
        : new P(function (resolve) {
            resolve(value);
          });
    }
    return new (P || (P = Promise))(function (resolve, reject) {
      function fulfilled(value) {
        try {
          step(generator.next(value));
        } catch (e) {
          reject(e);
        }
      }
      function rejected(value) {
        try {
          step(generator['throw'](value));
        } catch (e) {
          reject(e);
        }
      }
      function step(result) {
        result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
      }
      step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
  };
import { WeaviateInvalidInputError } from '../../errors.js';
import { toBase64FromMedia } from '../../index.js';
import { Deserialize } from '../deserialize/index.js';
import { Check } from '../query/check.js';
import { Serialize } from '../serialize/index.js';
class GenerateManager {
  constructor(check) {
    this.check = check;
  }
  static use(connection, name, dbVersionSupport, consistencyLevel, tenant) {
    return new GenerateManager(new Check(connection, name, dbVersionSupport, consistencyLevel, tenant));
  }
  parseReply(reply) {
    return __awaiter(this, void 0, void 0, function* () {
      const deserialize = yield Deserialize.use(this.check.dbVersionSupport);
      return deserialize.generate(reply);
    });
  }
  parseGroupByReply(opts, reply) {
    return __awaiter(this, void 0, void 0, function* () {
      const deserialize = yield Deserialize.use(this.check.dbVersionSupport);
      return Serialize.search.isGroupBy(opts)
        ? deserialize.generateGroupBy(reply)
        : deserialize.generate(reply);
    });
  }
  fetchObjects(generate, opts) {
    return Promise.all([
      this.check.fetchObjects(opts),
      this.check.supportForSingleGroupedGenerative(),
      this.check.supportForGenerativeConfigRuntime(generate.config),
    ])
      .then(([{ search }, supportsSingleGrouped]) =>
        __awaiter(this, void 0, void 0, function* () {
          return search.withFetch(
            Object.assign(Object.assign({}, Serialize.search.fetchObjects(opts)), {
              generative: yield Serialize.generative({ supportsSingleGrouped }, generate),
            })
          );
        })
      )
      .then((reply) => this.parseReply(reply));
  }
  bm25(query, generate, opts) {
    return Promise.all([
      this.check.bm25(opts),
      this.check.supportForSingleGroupedGenerative(),
      this.check.supportForGenerativeConfigRuntime(generate.config),
    ])
      .then(([{ search }, supportsSingleGrouped]) =>
        __awaiter(this, void 0, void 0, function* () {
          return search.withBm25(
            Object.assign(Object.assign({}, Serialize.search.bm25(query, opts)), {
              generative: yield Serialize.generative({ supportsSingleGrouped }, generate),
            })
          );
        })
      )
      .then((reply) => this.parseGroupByReply(opts, reply));
  }
  hybrid(query, generate, opts) {
    return Promise.all([
      this.check.hybridSearch(opts),
      this.check.supportForSingleGroupedGenerative(),
      this.check.supportForGenerativeConfigRuntime(generate.config),
    ])
      .then(
        ([
          { search, supportsTargets, supportsVectorsForTargets, supportsWeightsForTargets },
          supportsSingleGrouped,
        ]) =>
          __awaiter(this, void 0, void 0, function* () {
            return search.withHybrid(
              Object.assign(
                Object.assign(
                  {},
                  Serialize.search.hybrid(
                    {
                      query,
                      supportsTargets,
                      supportsVectorsForTargets,
                      supportsWeightsForTargets,
                    },
                    opts
                  )
                ),
                { generative: yield Serialize.generative({ supportsSingleGrouped }, generate) }
              )
            );
          })
      )
      .then((reply) => this.parseGroupByReply(opts, reply));
  }
  nearImage(image, generate, opts) {
    return Promise.all([
      this.check.nearSearch(opts),
      this.check.supportForSingleGroupedGenerative(),
      this.check.supportForGenerativeConfigRuntime(generate.config),
    ])
      .then(([{ search, supportsTargets, supportsWeightsForTargets }, supportsSingleGrouped]) =>
        Promise.all([
          toBase64FromMedia(image),
          Serialize.generative({ supportsSingleGrouped }, generate),
        ]).then(([image, generative]) =>
          search.withNearImage(
            Object.assign(
              Object.assign(
                {},
                Serialize.search.nearImage(
                  {
                    image,
                    supportsTargets,
                    supportsWeightsForTargets,
                  },
                  opts
                )
              ),
              { generative }
            )
          )
        )
      )
      .then((reply) => this.parseGroupByReply(opts, reply));
  }
  nearObject(id, generate, opts) {
    return Promise.all([
      this.check.nearSearch(opts),
      this.check.supportForSingleGroupedGenerative(),
      this.check.supportForGenerativeConfigRuntime(generate.config),
    ])
      .then(([{ search, supportsTargets, supportsWeightsForTargets }, supportsSingleGrouped]) =>
        __awaiter(this, void 0, void 0, function* () {
          return search.withNearObject(
            Object.assign(
              Object.assign(
                {},
                Serialize.search.nearObject(
                  {
                    id,
                    supportsTargets,
                    supportsWeightsForTargets,
                  },
                  opts
                )
              ),
              { generative: yield Serialize.generative({ supportsSingleGrouped }, generate) }
            )
          );
        })
      )
      .then((reply) => this.parseGroupByReply(opts, reply));
  }
  nearText(query, generate, opts) {
    return Promise.all([
      this.check.nearSearch(opts),
      this.check.supportForSingleGroupedGenerative(),
      this.check.supportForGenerativeConfigRuntime(generate.config),
    ])
      .then(([{ search, supportsTargets, supportsWeightsForTargets }, supportsSingleGrouped]) =>
        __awaiter(this, void 0, void 0, function* () {
          return search.withNearText(
            Object.assign(
              Object.assign(
                {},
                Serialize.search.nearText(
                  {
                    query,
                    supportsTargets,
                    supportsWeightsForTargets,
                  },
                  opts
                )
              ),
              { generative: yield Serialize.generative({ supportsSingleGrouped }, generate) }
            )
          );
        })
      )
      .then((reply) => this.parseGroupByReply(opts, reply));
  }
  nearVector(vector, generate, opts) {
    return Promise.all([
      this.check.nearVector(vector, opts),
      this.check.supportForSingleGroupedGenerative(),
      this.check.supportForGenerativeConfigRuntime(generate.config),
    ])
      .then(
        ([
          { search, supportsTargets, supportsVectorsForTargets, supportsWeightsForTargets },
          supportsSingleGrouped,
        ]) =>
          __awaiter(this, void 0, void 0, function* () {
            return search.withNearVector(
              Object.assign(
                Object.assign(
                  {},
                  Serialize.search.nearVector(
                    {
                      vector,
                      supportsTargets,
                      supportsVectorsForTargets,
                      supportsWeightsForTargets,
                    },
                    opts
                  )
                ),
                { generative: yield Serialize.generative({ supportsSingleGrouped }, generate) }
              )
            );
          })
      )
      .then((reply) => this.parseGroupByReply(opts, reply));
  }
  nearMedia(media, type, generate, opts) {
    return Promise.all([
      this.check.nearSearch(opts),
      this.check.supportForSingleGroupedGenerative(),
      this.check.supportForGenerativeConfigRuntime(generate.config),
    ])
      .then(([{ search, supportsTargets, supportsWeightsForTargets }, supportsSingleGrouped]) => {
        const args = {
          supportsTargets,
          supportsWeightsForTargets,
        };
        let send;
        switch (type) {
          case 'audio':
            send = (media, generative) =>
              search.withNearAudio(
                Object.assign(
                  Object.assign({}, Serialize.search.nearAudio(Object.assign({ audio: media }, args), opts)),
                  { generative }
                )
              );
            break;
          case 'depth':
            send = (media, generative) =>
              search.withNearDepth(
                Object.assign(
                  Object.assign({}, Serialize.search.nearDepth(Object.assign({ depth: media }, args), opts)),
                  { generative }
                )
              );
            break;
          case 'image':
            send = (media, generative) =>
              search.withNearImage(
                Object.assign(
                  Object.assign({}, Serialize.search.nearImage(Object.assign({ image: media }, args), opts)),
                  { generative }
                )
              );
            break;
          case 'imu':
            send = (media, generative) =>
              search.withNearIMU(
                Object.assign(
                  Object.assign({}, Serialize.search.nearIMU(Object.assign({ imu: media }, args), opts)),
                  { generative }
                )
              );
            break;
          case 'thermal':
            send = (media, generative) =>
              search.withNearThermal(
                Object.assign(
                  Object.assign(
                    {},
                    Serialize.search.nearThermal(Object.assign({ thermal: media }, args), opts)
                  ),
                  { generative }
                )
              );
            break;
          case 'video':
            send = (media, generative) =>
              search.withNearVideo(
                Object.assign(
                  Object.assign({}, Serialize.search.nearVideo(Object.assign({ video: media }, args))),
                  { generative }
                )
              );
            break;
          default:
            throw new WeaviateInvalidInputError(`Invalid media type: ${type}`);
        }
        return Promise.all([
          toBase64FromMedia(media),
          Serialize.generative({ supportsSingleGrouped }, generate),
        ]).then(([media, generative]) => send(media, generative));
      })
      .then((reply) => this.parseGroupByReply(opts, reply));
  }
}
export default GenerateManager.use;
export { generativeParameters } from './config.js';
