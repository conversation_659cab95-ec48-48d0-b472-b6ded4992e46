{"name": "moodifyme-ai-assistant", "version": "1.1.0", "description": "AI-powered emotional wellness assistant with crisis intervention and therapeutic support", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "setup": "node scripts/setupVectorStore.js", "test-cli": "node scripts/testCli.js", "lint": "eslint src/", "format": "prettier --write src/"}, "keywords": ["chatbot", "rag", "mood", "crisis-intervention", "mental-health", "emotional-wellness", "therapeutic-ai", "suicide-prevention", "langchain", "pinecone", "gemini"], "author": "Your Name", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.21.0", "@langchain/community": "^0.3.0", "@langchain/core": "^0.3.0", "@langchain/google-genai": "^0.1.0", "@langchain/pinecone": "^0.1.0", "@pinecone-database/pinecone": "^6.0.0", "chalk": "^4.1.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "inquirer": "^8.2.6", "langchain": "^0.3.0", "mammoth": "^1.6.0", "natural": "^6.7.0", "pdf-parse": "^1.1.1", "pdf2pic": "^2.1.4", "readline": "^1.3.0", "sentiment": "^5.0.2", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^20.10.5", "eslint": "^8.56.0", "nodemon": "^3.0.2", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0"}}