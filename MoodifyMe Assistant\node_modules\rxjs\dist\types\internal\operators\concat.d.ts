import { ObservableInputTuple, OperatorFunction, SchedulerLike } from '../types';
/** @deprecated Replaced with {@link concatWith}. Will be removed in v8. */
export declare function concat<T, A extends readonly unknown[]>(...sources: [...ObservableInputTuple<A>]): OperatorFunction<T, T | A[number]>;
/** @deprecated Replaced with {@link concatWith}. Will be removed in v8. */
export declare function concat<T, A extends readonly unknown[]>(...sourcesAndScheduler: [...ObservableInputTuple<A>, SchedulerLike]): OperatorFunction<T, T | A[number]>;
//# sourceMappingURL=concat.d.ts.map