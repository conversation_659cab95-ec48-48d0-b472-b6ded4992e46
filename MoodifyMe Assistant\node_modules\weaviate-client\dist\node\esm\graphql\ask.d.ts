export interface AskArgs {
  autocorrect?: boolean;
  certainty?: number;
  distance?: number;
  properties?: string[];
  question?: string;
  rerank?: boolean;
}
export default class GraphQLAsk {
  private autocorrect?;
  private certainty?;
  private distance?;
  private properties?;
  private question?;
  private rerank?;
  constructor(args: AskArgs);
  toString(wrap?: boolean): string;
  validate(): void;
}
