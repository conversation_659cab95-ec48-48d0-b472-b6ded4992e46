import ConceptsGetter from './conceptsGetter.js';
import ExtensionCreator from './extensionCreator.js';
const c11y = (client) => {
  return {
    conceptsGetter: () => new ConceptsGetter(client),
    extensionCreator: () => new ExtensionCreator(client),
  };
};
export default c11y;
export { default as ConceptsGetter } from './conceptsGetter.js';
export { default as ExtensionCreator } from './extensionCreator.js';
