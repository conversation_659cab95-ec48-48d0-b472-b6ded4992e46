define(['./restArguments', './_flatten', './filter', './contains'], function (restArguments, _flatten, filter, contains) {

  // Take the difference between one array and a number of other arrays.
  // Only the elements present in just the first array will remain.
  var difference = restArguments(function(array, rest) {
    rest = _flatten(rest, true, true);
    return filter(array, function(value){
      return !contains(rest, value);
    });
  });

  return difference;

});
